@echo off
chcp 65001 >nul
echo ========================================
echo    Student Quality Assessment System
echo ========================================
echo.

echo Starting backend server...
cd server
start "Backend Server" cmd /k "node sqlite-server.js"
cd ..

echo Waiting for backend server to start...
timeout /t 3 /nobreak >nul

echo Starting frontend server...
cd client
start "Frontend Server" cmd /k "npm run dev"
cd ..

echo.
echo ========================================
echo System Started Successfully!
echo ========================================
echo Frontend: http://localhost:3000
echo Backend:  http://localhost:5000
echo.
echo Admin Account:
echo Username: admin
echo Password: admin123
echo ========================================
echo.
echo Press any key to exit...
pause >nul
