# 📚 学生素质测评系统使用说明

## 🎯 证书提交规则详解

### 📊 提交数量限制

| 证书类别 | 提交限制 | 说明 |
|----------|----------|------|
| 学科竞赛 | 无限制 | 可提交多个，累计加分，封顶30分 |
| 实践技能类竞赛 | 无限制 | 可提交多个，累计加分，封顶30分 |
| 学生创新型项目 | 无限制 | 可提交多个，累计加分，封顶30分 |
| 专业认证 | 无限制 | 可提交多个，累计加分，封顶30分 |
| 科学研究 | **最多2篇** | 学术论文每人限报2篇 |
| 体育竞赛 | **最多2项** | 每人限报2项，满分10分 |
| 文艺活动 | **最多2项** | 每人限报2项，满分10分 |
| 英语六级 | 1项 | 通过即可加6分 |
| 学生干部 | 1项 | 按最高职务加分，不累计 |
| 社会实践志愿服务 | 无限制 | 满分10分 |
| 新闻采编写 | 无限制 | 每次0.1分，上限2分 |
| 见义勇为等表彰 | 无限制 | 根据级别加分 |

### 🚫 同一项目限制

**重要原则**：同一项目只能作为一类加分项计算

**检查规则**：
- 证书名称相同
- 获奖日期相同
- 系统自动检测并拒绝重复提交

**示例**：
- ✅ 正确：参加"全国大学生数学建模竞赛"获得省级一等奖，只在"学科竞赛"类别提交
- ❌ 错误：同一个"全国大学生数学建模竞赛"省级一等奖，既在"学科竞赛"又在"实践技能类竞赛"提交

### 👥 团队项目规则

#### 需要填写团队信息的类别：
1. **实践技能类竞赛**
   - 2-3人团队：分数÷2
   - 3人以上团队：分数÷3

2. **体育竞赛**
   - 2-3人团队：分数÷2
   - 3人以上团队：分数÷3

3. **文艺活动**
   - 2-3人团队：分数÷2
   - 3人以上团队：分数÷3

#### 需要填写角色信息的类别：
1. **学生创新型项目**
   - 主持人：获得完整分数
   - 参与成员：获得相应比例分数
   - 不按团队人数调整

#### 不需要团队信息的类别：
- 学科竞赛
- 专业认证
- 科学研究
- 英语六级
- 学生干部
- 社会实践志愿服务
- 新闻采编写
- 见义勇为等表彰

## 📝 证书提交操作指南

### 学生端操作步骤

1. **登录系统**
   - 使用学号和密码登录
   - 默认密码为学号

2. **单个证书提交**
   - 进入"证书管理"页面
   - 点击"提交证书"按钮
   - 选择证书类别（系统会自动显示相应字段）
   - 填写证书信息：
     - 证书名称（必填）
     - 获奖等级（必填）
     - 获奖日期（选填）
     - 团队信息（根据类别显示）
     - 角色信息（学生创新型项目显示）
   - 上传证书文件
   - 点击"提交"

3. **批量证书提交**
   - 点击"批量提交"按钮
   - 填写多个证书信息
   - 点击"添加更多证书"增加证书项
   - 上传对应的证书文件
   - 点击"批量提交"

4. **查看提交状态**
   - 待审核：黄色标签
   - 审核通过：绿色标签
   - 审核拒绝：红色标签

### 教师端操作指南

1. **证书审核**
   - 进入"证书审核"页面
   - 查看待审核证书列表
   - 点击"审核"按钮
   - 选择"通过"或"拒绝"
   - 填写审核意见
   - 确认审核

2. **成绩管理**
   - 进入"成绩管理"页面
   - 查看学生成绩排行榜
   - 单个修改：点击"修改"按钮
   - 批量修改：选择学生后点击"批量修改"
   - 重新计算：点击"重新计算"按钮

3. **系统设置**
   - 进入"系统设置"页面
   - 设置证书提交时间窗口
   - 配置系统公告
   - 开启/关闭证书提交功能

## ⚠️ 注意事项

### 证书提交注意事项

1. **时间限制**
   - 注意系统设置的提交时间窗口
   - 超过截止时间无法提交

2. **文件要求**
   - 支持图片格式：JPG、PNG、GIF
   - 支持文档格式：PDF
   - 文件大小不超过10MB

3. **信息准确性**
   - 确保证书信息真实准确
   - 获奖等级选择正确
   - 团队信息如实填写

4. **重复提交**
   - 系统会自动检测重复项目
   - 同一项目不能重复提交

### 成绩计算说明

1. **自动计算**
   - 系统按照细则自动计算分数
   - 各类别有分值上限
   - 取最高分项目计分

2. **手动调整**
   - 教师可以手动修改成绩
   - 手动成绩优先于自动计算
   - 支持批量调整

3. **最终成绩**
   - 最终成绩 = 总分值 × 20%
   - 保留两位小数

## 🔧 常见问题解决

### 1. 无法提交证书
- 检查是否在提交时间窗口内
- 确认该类别是否已达到提交上限
- 检查是否重复提交同一项目

### 2. 成绩计算异常
- 确认证书是否已审核通过
- 检查团队信息是否正确
- 联系教师重新计算成绩

### 3. 文件上传失败
- 检查文件格式是否支持
- 确认文件大小不超过10MB
- 检查网络连接状态

### 4. 登录问题
- 确认用户名密码正确
- 默认密码为学号
- 联系管理员重置密码

---

**系统版本**: v2.0.0  
**最后更新**: 2025年7月5日  
**技术支持**: 学生素质测评系统开发团队
