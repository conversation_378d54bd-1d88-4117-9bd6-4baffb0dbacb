@echo off
chcp 65001 >nul
title 学生素质测评系统 - 一键部署

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    学生素质测评系统                          ║
echo ║                      一键部署工具                            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 正在检查系统环境...

:: 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Node.js，请先安装Node.js
    echo 📥 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js版本: %NODE_VERSION%

:: 检查npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm未正确安装
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ npm版本: %NPM_VERSION%

echo.
echo 📦 正在安装项目依赖...
call npm install
if errorlevel 1 (
    echo ❌ 依赖安装失败，请检查网络连接
    pause
    exit /b 1
)

echo.
echo 🏗️ 正在构建前端应用...
cd client
call npm run build
if errorlevel 1 (
    echo ❌ 前端构建失败
    cd ..
    pause
    exit /b 1
)
cd ..

echo.
echo ⚙️ 正在配置生产环境...
if not exist "server\.env" (
    copy "server\.env.production" "server\.env"
    echo ✅ 已创建环境配置文件
)

echo.
echo 🌐 正在获取本机IP地址...
for /f "tokens=2 delims=:" %%i in ('ipconfig ^| findstr /i "IPv4"') do (
    for /f "tokens=1" %%j in ("%%i") do (
        set LOCAL_IP=%%j
        goto :found_ip
    )
)
:found_ip

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        部署完成！                            ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║  本地访问地址: http://localhost:5000                         ║
echo ║  局域网访问:   http://%LOCAL_IP%:5000                        ║
echo ║                                                              ║
echo ║  管理员账户:                                                 ║
echo ║  用户名: admin                                               ║
echo ║  密码:   admin123                                            ║
echo ║                                                              ║
echo ║  💡 提示: 请确保防火墙允许端口5000通过                       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 正在启动服务器...
echo 📝 按 Ctrl+C 可停止服务器
echo.

cd server
node sqlite-server.js

pause
