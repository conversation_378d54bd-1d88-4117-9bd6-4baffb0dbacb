# 🎓 学生素质测评系统

基于河南大学素质测评细则开发的完整管理系统，支持证书提交、审核、成绩计算和Excel导入导出。

**版本**: v2.0.0
**开发日期**: 2025年7月5日
**技术栈**: React + Node.js + SQLite

## ✨ 核心功能

### 🎯 学生端功能
- **证书提交**：支持单个和批量提交，智能表单验证
- **成绩查看**：实时查看个人素质测评成绩
- **进度跟踪**：查看证书审核状态和成绩构成

### 👨‍🏫 教师端功能
- **证书审核**：批量审核学生提交的证书材料
- **成绩管理**：单个和批量修改学生成绩
- **Excel管理**：导入学生信息，导出成绩报表
- **系统设置**：配置证书提交时间窗口和系统公告

### 📊 智能计算
- **按细则计算**：严格按照河南大学素质测评细则计算分数
- **团队分数调整**：自动应用团队项目分数调整规则
- **分值上限控制**：各类别自动应用分值上限
- **重复检查**：防止同一项目重复加分

## 🛠️ 技术架构

### 前端技术栈
- **React 18** - 现代化前端框架
- **Vite** - 快速构建工具
- **Ant Design** - 企业级UI组件库
- **Axios** - HTTP客户端

### 后端技术栈
- **Node.js** - 服务器运行环境
- **Express** - Web应用框架
- **SQLite** - 轻量级数据库
- **JWT** - 身份认证
- **ExcelJS** - Excel文件处理
- **Multer** - 文件上传处理

## 🚀 快速启动

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 启动步骤

1. **启动后端服务器**
   ```bash
   cd server
   node sqlite-server.js
   ```

2. **启动前端服务器**
   ```bash
   cd client
   npm run dev
   ```

3. **访问系统**
   - 前端地址：http://localhost:3000
   - 后端API：http://localhost:5000

### 一键启动
- Windows：双击 `start.bat` 或 `start.ps1`

## 🔑 系统账户

### 管理员账户
- **用户名**：`admin`
- **密码**：`admin123`
- **权限**：完整系统管理权限

### 学生账户
- **说明**：通过Excel导入或手动添加
- **默认密码**：学号
- **权限**：学生功能权限

## 📋 功能详解

### 证书提交规则
1. **提交数量限制**：
   - 体育竞赛：每人限报2项
   - 文艺活动：每人限报2项
   - 学术论文：每人限报2篇
   - 其他类别：无明确数量限制

2. **同一项目限制**：
   - 同一项目只能作为一类加分项计算
   - 不得重复提交相同项目获得多重加分

3. **团队项目规则**：
   - **需要团队信息**：实践技能类竞赛、体育竞赛、文艺活动
   - **需要角色信息**：学生创新型项目（区分主持人和成员）
   - **不需要团队信息**：学科竞赛、专业认证、科学研究等

### 成绩计算规则
- **基础分值**：25分（可因考勤、违纪等扣分）
- **专业类活动**：累计加分，封顶30分
- **体育美育**：累计加分，封顶20分
- **文明品德**：累计加分，封顶25分
- **最终成绩**：总分值 × 20%

## 📁 项目结构

```
学生素质测评系统/
├── client/                 # 前端代码
│   ├── src/
│   │   ├── pages/         # 页面组件
│   │   ├── components/    # 通用组件
│   │   ├── utils/         # 工具函数
│   │   └── contexts/      # React上下文
│   ├── package.json
│   └── vite.config.js
├── server/                # 后端代码
│   ├── sqlite-server.js   # 主服务器文件
│   ├── uploads/           # 文件上传目录
│   └── package.json
├── start.bat              # Windows启动脚本
├── start.ps1              # PowerShell启动脚本
├── 素质测评成绩计算细则.md  # 详细计算规则
├── 系统使用说明.md         # 使用指南
└── 运行指南.md            # 运行说明
```

## 🎯 使用指南

### 教师操作流程
1. 使用管理员账户登录
2. 通过Excel导入学生信息
3. 学生提交证书后进行审核
4. 查看和管理学生成绩
5. 导出最终成绩报表

### 学生操作流程
1. 使用学号和密码登录
2. 提交各类证书材料
3. 查看证书审核状态
4. 查看个人成绩详情

## 🔧 系统维护

### 数据备份
- 系统使用SQLite内存数据库
- 重启服务器会重置所有数据
- 生产环境建议使用持久化数据库

### 文件管理
- 上传的证书文件存储在 `server/uploads/` 目录
- 定期清理临时文件和无效上传

### 系统监控
- 查看后端控制台日志
- 监控API请求状态
- 检查文件上传情况

## 📞 技术支持

### 常见问题
1. **无法启动**：检查Node.js版本和端口占用
2. **登录失败**：确认用户名密码正确
3. **文件上传失败**：检查文件格式和大小
4. **成绩计算异常**：确认证书审核状态

### 联系方式
- 开发团队：学生素质测评系统开发组
- 技术支持：系统管理员
- 更新日期：2025年7月5日

---

**版本信息**：v2.0.0  
**开发框架**：React + Node.js  
**数据库**：SQLite  
**部署方式**：本地部署
