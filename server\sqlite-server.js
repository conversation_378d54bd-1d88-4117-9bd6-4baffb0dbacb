require('dotenv').config();
const express = require('express');
const cors = require('cors');
const jwt = require('jsonwebtoken');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = process.env.PORT || 5000;
// JWT密钥 - 在生产环境中应该使用环境变量
const JWT_SECRET = process.env.JWT_SECRET || 'demo_secret_key_' + Date.now();

// 安全设置
const ENABLE_RATE_LIMIT = process.env.ENABLE_RATE_LIMIT !== 'false';
const ENABLE_SECURITY_CHECKS = process.env.ENABLE_SECURITY_CHECKS !== 'false';
const STRICT_MODE = process.env.STRICT_MODE !== 'false';

// 日志设置
const ENABLE_ADMIN_DETAILED_LOGS = process.env.ENABLE_ADMIN_DETAILED_LOGS === 'true';

// 令牌黑名单（用于登出和撤销）
const tokenBlacklist = new Set();

// 数据持久化文件路径
const DATA_FILE = path.join(__dirname, 'data.json');

// 数据加载函数
function loadData() {
  try {
    if (fs.existsSync(DATA_FILE)) {
      const data = JSON.parse(fs.readFileSync(DATA_FILE, 'utf8'));
      console.log('📂 从文件加载数据:', {
        users: data.users?.length || 0,
        certificates: data.certificates?.length || 0
      });
      return data;
    }
  } catch (error) {
    console.error('❌ 加载数据文件失败:', error);
  }
  return null;
}

// 记录操作日志
function logOperation(userId, action, details = {}, targetType = null, targetId = null, req = null) {
  // 获取用户信息
  const user = users.find(u => u.id === userId);
  const isSystemAdmin = user && user.is_super_admin && user.username === 'admin';
  const isStudent = user && user.role === 'student';

  // 不记录普通学生的操作
  if (isStudent) {
    // 只记录学生的重要操作，如证书提交
    const studentImportantActions = [
      '提交证书',
      '删除证书',
      '修改证书'
    ];

    if (!studentImportantActions.includes(action)) {
      return null; // 不记录普通学生操作，包括登录失败等
    }
  }

  // 对系统管理员的操作进行简化记录
  let simplifiedAction = action;
  let simplifiedDetails = details;

  if (isSystemAdmin && !ENABLE_ADMIN_DETAILED_LOGS) {
    // 系统管理员操作简化规则
    const adminSimplifiedActions = {
      '查看管理员列表': '管理员操作',
      '创建管理员': '管理员操作',
      '更新管理员权限': '管理员操作',
      '删除管理员': '管理员操作',
      '重置管理员密码': '管理员操作',
      '删除操作日志': '日志管理',
      '批量删除操作日志': '日志管理',
      '清空所有操作日志': '日志管理',
      '导出成绩': '数据导出',
      '创建审核活动': '活动管理',
      '审核通过证书': '证书审核',
      '审核拒绝证书': '证书审核'
    };

    // 如果是需要简化的操作，使用简化的action和details
    if (adminSimplifiedActions[action]) {
      simplifiedAction = adminSimplifiedActions[action];
      simplifiedDetails = { operation_type: action }; // 只保留操作类型
    }

    // 对于频繁的查看操作，不记录到数据库
    const skipDatabaseLogging = [
      '查看管理员列表',
      '获取操作日志',
      '获取系统统计',
      '获取审核活动列表',
      '获取证书列表',
      '获取学生列表'
    ];

    if (skipDatabaseLogging.includes(action)) {
      // 只在控制台显示简化信息，不存储到数据库
      console.log(`📝 系统管理员: ${action}`);
      return null;
    }
  }

  // 过滤敏感信息
  let filteredDetails = { ...simplifiedDetails };
  if (filteredDetails) {
    // 删除敏感字段
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];
    sensitiveFields.forEach(field => {
      if (filteredDetails[field]) {
        delete filteredDetails[field];
      }
    });

    // 如果是登录操作，只保留基本信息
    if (action === '用户登录') {
      filteredDetails = {
        login_method: filteredDetails.login_method,
        user_role: filteredDetails.user_role,
        user_name: filteredDetails.user_name
      };
    }
  }

  const log = {
    id: nextLogId++,
    user_id: userId,
    action: simplifiedAction,
    details: filteredDetails,
    target_type: targetType,
    target_id: targetId,
    ip_address: req ? (req.ip || req.connection.remoteAddress) : null,
    user_agent: req ? req.get('User-Agent') : null,
    created_at: new Date().toISOString()
  };

  operationLogs.push(log);

  // 控制台日志显示
  if (isSystemAdmin) {
    console.log(`📝 系统管理员: ${simplifiedAction}`);
  } else if (!isStudent) {
    console.log(`📝 操作日志: ${user?.name || user?.username || 'Unknown'}(${user?.student_id || user?.username}) - ${action}`);
  }

  return log;
}

// 数据保存函数
function saveData() {
  try {
    const data = {
      users: users,
      certificates: certificates,
      auditActivities: auditActivities,
      operationLogs: operationLogs,
      lastUpdated: new Date().toISOString()
    };
    fs.writeFileSync(DATA_FILE, JSON.stringify(data, null, 2));
    console.log('💾 数据已保存到文件');
  } catch (error) {
    console.error('❌ 保存数据文件失败:', error);
  }
}

// 请求频率限制 - 已禁用，仅记录日志
const requestCounts = new Map();
const RATE_LIMIT_WINDOW = 60 * 1000; // 1分钟
const MAX_REQUESTS_PER_MINUTE = 10000; // 大幅提高限制到每分钟10000个请求
const MAX_REQUESTS_PER_MINUTE_AUTH = 1000; // 认证相关接口每分钟1000个请求

const rateLimitMiddleware = (req, res, next) => {
  // 检查是否启用频率限制
  if (!ENABLE_RATE_LIMIT) {
    return next(); // 完全跳过频率限制
  }

  const clientIp = req.ip;
  const now = Date.now();
  const isAuthRequest = req.path.includes('/auth/') || req.path.includes('/login');
  const maxRequests = isAuthRequest ? MAX_REQUESTS_PER_MINUTE_AUTH : MAX_REQUESTS_PER_MINUTE;

  // 清理过期的记录
  for (const [ip, data] of requestCounts.entries()) {
    if (now - data.windowStart > RATE_LIMIT_WINDOW) {
      requestCounts.delete(ip);
    }
  }

  const clientData = requestCounts.get(clientIp) || { count: 0, windowStart: now };

  // 如果是新的时间窗口，重置计数
  if (now - clientData.windowStart > RATE_LIMIT_WINDOW) {
    clientData.count = 0;
    clientData.windowStart = now;
  }

  clientData.count++;
  requestCounts.set(clientIp, clientData);

  // 仅在严格模式下才限制请求
  if (STRICT_MODE && clientData.count > maxRequests) {
    console.log('🚨 请求频率过高:', {
      ip: clientIp,
      count: clientData.count,
      path: req.path,
      userAgent: req.get('User-Agent')?.substring(0, 50)
    });

    return res.status(429).json({
      message: '请求过于频繁，请稍后再试',
      retryAfter: Math.ceil((RATE_LIMIT_WINDOW - (now - clientData.windowStart)) / 1000)
    });
  }

  next();
};

// 输入验证中间件
const inputValidationMiddleware = (req, res, next) => {
  // 检查是否启用安全检查
  if (!ENABLE_SECURITY_CHECKS) {
    return next(); // 完全跳过安全检查
  }

  // 检查请求体大小
  const contentLength = parseInt(req.get('Content-Length') || '0');
  if (contentLength > 50 * 1024 * 1024) { // 50MB
    if (STRICT_MODE) {
      return res.status(413).json({ message: '请求体过大' });
    } else {
      console.log('⚠️ 请求体较大（仅记录）:', {
        ip: req.ip,
        size: contentLength,
        path: req.path
      });
    }
  }

  next();
};

// 中间件
app.use(cors());
app.use(express.json({ limit: '50mb' })); // 提高到50MB
app.use(express.urlencoded({ extended: true, limit: '50mb' })); // 提高到50MB
app.use(rateLimitMiddleware); // 已禁用限制功能
app.use(inputValidationMiddleware); // 已大幅放宽限制

// 静态文件服务 - 服务前端构建文件
app.use(express.static(path.join(__dirname, '../client/dist')));

// 确保uploads目录存在
if (!fs.existsSync('uploads')) {
  fs.mkdirSync('uploads');
}

// 静态文件服务 - 提供上传文件访问
app.use('/api/uploads', express.static(path.join(__dirname, 'uploads'), {
  setHeaders: (res, path) => {
    res.setHeader('Cache-Control', 'public, max-age=31536000');
  }
}));

// 静态文件服务 - 提供前端文件访问（用于生产环境）
const clientDistPath = path.join(__dirname, '../client/dist');
if (fs.existsSync(clientDistPath)) {
  console.log('✅ 找到前端构建文件，启用静态文件服务');
  app.use(express.static(clientDistPath));

  // 处理前端路由 - 所有非API请求都返回index.html
  app.get('*', (req, res, next) => {
    // 跳过API请求
    if (req.path.startsWith('/api/')) {
      return next();
    }

    res.sendFile(path.join(clientDistPath, 'index.html'));
  });
} else {
  console.log('⚠️  未找到前端构建文件，请运行 npm run build-client');
}

// 文件上传配置
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/');
  },
  filename: function (req, file, cb) {
    // 保留原始文件扩展名
    const ext = path.extname(file.originalname);
    const name = Date.now() + '-' + Math.round(Math.random() * 1E9) + ext;
    cb(null, name);
  }
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 50 * 1024 * 1024 }, // 提高到50MB
  fileFilter: function (req, file, cb) {
    // 只允许图片格式
    const allowedMimes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/bmp',
      'image/webp',
      'image/svg+xml'
    ];

    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('只允许上传图片文件 (支持格式: JPG, PNG, GIF, BMP, WebP, SVG)'));
    }
  }
});

// 内存数据库 - 从文件加载或使用默认值
const savedData = loadData();

let users = savedData?.users || [
  {
    id: 1,
    username: 'admin',
    student_id: 'admin',
    name: '超级管理员',
    class_name: '管理员',
    password: 'admin123',
    role: 'teacher',
    is_super_admin: true,
    permissions: Object.values(PERMISSIONS),
    created_at: new Date().toISOString(),
    last_login: null
  }
];

let certificates = savedData?.certificates || [];
let nextCertId = savedData?.certificates ? Math.max(...savedData.certificates.map(c => c.id), 0) + 1 : 1;
let nextUserId = savedData?.users ? Math.max(...savedData.users.map(u => u.id), 0) + 1 : 2;

// 审核活动数据
let auditActivities = savedData?.auditActivities || [];
let nextActivityId = savedData?.auditActivities ? Math.max(...savedData.auditActivities.map(a => a.id), 0) + 1 : 1;

// 如果没有审核活动，创建一个默认的
if (auditActivities.length === 0) {
  const defaultActivity = {
    id: nextActivityId++,
    name: '2025年度学生素质测评',
    title: '2025年度学生素质测评',
    description: '2025年度学生综合素质测评活动',
    start_date: '2025-01-01',
    end_date: '2025-12-31',
    status: 'active',
    created_at: new Date().toISOString(),
    categories: []
  };
  auditActivities.push(defaultActivity);
  console.log('🎯 创建默认审核活动:', defaultActivity.name);
}

// 操作日志数据
let operationLogs = savedData?.operationLogs || [];
let nextLogId = savedData?.operationLogs ? Math.max(...savedData.operationLogs.map(l => l.id), 0) + 1 : 1;

// 权限定义
const PERMISSIONS = {
  CERTIFICATE_AUDIT: 'certificate_audit',           // 证书审核
  STUDENT_MANAGEMENT: 'student_management',         // 学生管理
  GRADE_EXPORT: 'grade_export',                     // 成绩导出
  ACTIVITY_MANAGEMENT: 'activity_management',       // 审核活动管理
  CATEGORY_MANAGEMENT: 'category_management',       // 证书类别管理
  ADMIN_MANAGEMENT: 'admin_management',             // 管理员管理（仅超级管理员）
  SYSTEM_SETTINGS: 'system_settings',               // 系统设置
  DATA_CLEANUP: 'data_cleanup'                      // 数据清理
};

// 确保现有的admin用户是超级管理员
const adminUser = users.find(u => u.username === 'admin' || u.student_id === 'admin');
if (adminUser && !adminUser.is_super_admin) {
  adminUser.is_super_admin = true;
  adminUser.permissions = Object.values(PERMISSIONS);
  adminUser.username = adminUser.username || 'admin';
  saveData();
}

// 修复现有管理员账号的数据结构
users.forEach(user => {
  if (user.role === 'teacher') {
    // 确保管理员有 student_id 字段
    if (!user.student_id && user.username) {
      user.student_id = user.username;
    }
    // 确保管理员有 username 字段
    if (!user.username && user.student_id) {
      user.username = user.student_id;
    }
  }
});
saveData();

// 系统设置
let systemSettings = {
  certificate_submission_enabled: true,
  certificate_submission_deadline: null, // null表示无限制
  certificate_submission_start: null,    // null表示无限制
  announcement: '欢迎使用学生素质测评系统！请按照细则要求提交证书材料。'
};

// 证书类别（根据素质测评细则重新设计）
let categories = [
  // 专业类活动（30分）
  {
    id: 1,
    category_name: '学科竞赛',
    parent_id: null,
    max_score: 30,
    type: 'professional',
    requires_certificate_name: false,  // 改为false，从预定义列表选择
    requires_team_info: false,         // 学科竞赛不进行团队分值调整
    use_predefined_names: true,        // 使用预定义竞赛名称
    description: '依据学校教务处发布的学科竞赛目录，同一项目只能作为一类加分项计算',
    predefined_names: [
      '中国(国际)"互联网+"大学生创新创业大赛',
      '"挑战杯"中国大学生创业计划大赛',
      '"挑战杯"全国大学生课外学术科技作品竞赛',
      '全国大学生创新创业训练计划年会展示',
      '中国大学生服务外包创新创业大赛',
      '中国高校计算机大赛-大数据挑战赛',
      '中国高校计算机大赛-团体程序设计天梯赛',
      '中国高校计算机大赛-移动应用创新赛',
      '中国高校计算机大赛-网络技术挑战赛',
      '中国高校计算机大赛-人工智能创意赛',
      '中国高校计算机大赛-微信小程序应用开发赛',
      '全国大学生信息安全大赛',
      '中国机器人大赛暨RoboCup机器人世界杯中国赛',
      'ACM-ICPC国际大学生程序设计竞赛',
      '中国大学生计算机设计大赛',
      '蓝桥杯全国软件和信息技术专业人才大赛',
      '"中国软件杯"大学生软件设计大赛',
      '全国大学生数学建模竞赛',
      '"新华三杯"全国大学生数字技术大赛',
      '中国机器人及人工智能大赛',
      'RoboCom机器人开发者大赛',
      '华为ICT大赛',
      '中国高校智能机器人创意大赛',
      '中国大学生程序设计竞赛',
      '河南省"新工科"大学生工业设计大赛',
      '河南省高等学校信息安全对抗大赛',
      '黄河鲲鹏开发者大赛',
      '河南大学计算机程序设计类大赛',
      '百度之星·程序设计大赛',
      '全国大学生计算机系统能力大赛',
      '全国大学生物联网设计竞赛',
      '全国大学生信息安全与对抗技术竞赛',
      '全球校园人工智能算法精英大赛',
      '中国工程机器人大赛暨国际公开赛',
      '国际大学生智能农业装备创新大赛'
    ],
    level_options: [
      { value: 'national_1', label: '国家级一等奖' },
      { value: 'national_2', label: '国家级二等奖' },
      { value: 'national_3', label: '国家级三等奖' },
      { value: 'provincial_1', label: '省级一等奖' },
      { value: 'provincial_2', label: '省级二等奖' },
      { value: 'provincial_3', label: '省级三等奖' },
      { value: 'school_1', label: '校级一等奖' },
      { value: 'school_2', label: '校级二等奖' },
      { value: 'school_3', label: '校级三等奖' },
      { value: 'college_1', label: '院级一等奖' },
      { value: 'college_2', label: '院级二等奖' },
      { value: 'college_3', label: '院级三等奖' }
    ]
  },
  {
    id: 2,
    category_name: '实践技能类竞赛',
    parent_id: null,
    max_score: 30,
    type: 'professional',
    requires_certificate_name: true,
    requires_team_info: true,
    description: '实践技能类竞赛，根据等级和团队人数计算分值',
    level_options: [
      { value: 'national_1', label: '国家级一等奖' },
      { value: 'national_2', label: '国家级二等奖' },
      { value: 'national_3', label: '国家级三等奖' },
      { value: 'provincial_1', label: '省级一等奖' },
      { value: 'provincial_2', label: '省级二等奖' },
      { value: 'provincial_3', label: '省级三等奖' },
      { value: 'school_1', label: '校级一等奖' },
      { value: 'school_2', label: '校级二等奖' },
      { value: 'school_3', label: '校级三等奖' }
    ]
  },
  {
    id: 3,
    category_name: '学生创新型项目',
    parent_id: null,
    max_score: 30,
    type: 'professional',
    requires_certificate_name: true,
    requires_team_info: false,
    level_options: [
      { value: 'national_1', label: '国家级一等奖' },
      { value: 'national_2', label: '国家级二等奖' },
      { value: 'national_3', label: '国家级三等奖' },
      { value: 'provincial_1', label: '省级一等奖' },
      { value: 'provincial_2', label: '省级二等奖' },
      { value: 'provincial_3', label: '省级三等奖' },
      { value: 'school_1', label: '校级一等奖' },
      { value: 'school_2', label: '校级二等奖' },
      { value: 'school_3', label: '校级三等奖' }
    ]
  },
  {
    id: 4,
    category_name: '专业认证',
    parent_id: null,
    max_score: 30,
    type: 'professional',
    requires_certificate_name: false,
    requires_team_info: false,
    level_options: [
      { value: 'national', label: '国家级认证' },
      { value: 'industry', label: '行业认证' },
      { value: 'professional', label: '专业认证' }
    ]
  },
  {
    id: 5,
    category_name: '科学研究',
    parent_id: null,
    max_score: 30,
    type: 'professional',
    requires_certificate_name: true,
    requires_team_info: false,
    level_options: [
      { value: 'sci_1', label: 'SCI一区' },
      { value: 'sci_2', label: 'SCI二区' },
      { value: 'sci_3', label: 'SCI三区' },
      { value: 'sci_4', label: 'SCI四区' },
      { value: 'ei', label: 'EI期刊' },
      { value: 'core', label: '核心期刊' },
      { value: 'patent', label: '发明专利' }
    ]
  },
  {
    id: 6,
    category_name: '体育竞赛',
    parent_id: null,
    max_score: 20,
    type: 'sports_arts',
    requires_certificate_name: true,
    requires_team_info: true,
    description: '体育竞赛，按名次计分，每人限报2项，满分10分',
    level_options: [
      { value: 'national_1', label: '国家级第一名' },
      { value: 'national_2', label: '国家级第二名' },
      { value: 'national_3', label: '国家级第三名' },
      { value: 'national_other', label: '国家级其他名次（4-8名）' },
      { value: 'provincial_1', label: '省级第一名' },
      { value: 'provincial_2', label: '省级第二名' },
      { value: 'provincial_3', label: '省级第三名' },
      { value: 'provincial_other', label: '省级其他名次（4-8名）' },
      { value: 'school_1', label: '校级第一名' },
      { value: 'school_2', label: '校级第二名' },
      { value: 'school_3', label: '校级第三名' },
      { value: 'school_other', label: '校级其他名次（4-8名）' },
      { value: 'college_1', label: '院级第一名' },
      { value: 'college_2', label: '院级第二名' },
      { value: 'college_3', label: '院级第三名' },
      { value: 'college_other', label: '院级其他名次（不加分）' }
    ]
  },
  {
    id: 7,
    category_name: '文艺活动',
    parent_id: null,
    max_score: 20,
    type: 'sports_arts',
    requires_certificate_name: true,
    requires_team_info: true,
    use_predefined_names: true,        // 使用预定义名称
    allow_custom_input: true,          // 允许自定义输入
    description: '演讲比赛、征文活动、辩论赛、知识竞赛、文艺表演、歌手大赛、形象设计、动漫创作等各类活动，每人限报2项，满分10分',
    predefined_names: [
      // 全国性竞赛
      '全国大学生节能减排社会实践与科技竞赛',
      '"外研社·国才杯"全国大学生英语系列赛-英语演讲',
      '"外研社·国才杯"全国大学生英语系列赛-英语辩论',
      '"外研社·国才杯"全国大学生英语系列赛-英语写作',
      '"外研社·国才杯"全国大学生英语系列赛-英语阅读',
      '全国大学生电子设计竞赛',
      '全国大学生智能汽车竞赛',
      '全国大学生机器人大赛-RoboMaster',
      '全国大学生机器人大赛-RoboCon',
      '中国大学生机械工程创新创意大赛-过程装备实践与创新赛',
      '中国大学生机械工程创新创意大赛-铸造工艺设计赛',
      '中国大学生机械工程创新创意大赛-材料热处理创新创意赛',
      '中国大学生机械工程创新创意大赛-起重机创意赛',
      '"西门子杯"中国智能制造挑战赛',
      '全国大学生集成电路创新创业大赛',
      '全国大学生机械创新设计大赛',
      '全国大学生金相技能大赛',
      '全国大学生光电设计大赛',
      '全国大学生交通科技大赛',
      '全国大学生工程训练综合能力竞赛',
      '全国大学生先进成图技术与产品信息建模创新大赛',
      '全国周培源大学生力学竞赛',
      '全国大学生结构设计竞赛',
      '全国大学生化学实验邀请赛',
      '全国大学生化工设计竞赛',
      '全国高等医学院校大学生临床技能竞赛',
      '全国大学生电子商务"创新、创意及创业"挑战赛',
      '全国大学生物流设计大赛',
      '全国大学生市场调查与分析大赛',
      '全国大学生地质技能竞赛',
      '全国大学生广告艺术大赛',
      '全国高校数字艺术设计大赛',
      '两岸新锐设计竞赛"华灿奖"',
      '米兰设计周--中国高校设计学科师生优秀作品展',
      '中美青年创客大赛',
      '中国青年志愿服务项目大赛',
      '中国创新创业大赛',
      '"中国创翼"创业创新大赛',
      '"创客中国"中小企业创新创业大赛',
      '全国移动互联创新大赛',
      '全国"互联网+"快递大学生创新创业大赛',
      '全国大学生生命科学创新创业大赛',
      '全国大学生生命科学竞赛',
      '丘成桐大学生数学竞赛',
      '全国大学生数学竞赛',
      '美国大学生数学建模竞赛',
      '全国大学生物理实验竞赛',
      '全国高校无机非金属材料基础知识大赛',
      '"纳米之星"创新创业大赛总决赛',
      '中国大学生医学技术技能大赛',
      '全国护理专业本科临床技能大赛',
      '全国大学生药苑论坛',
      '全国中医药高等教育技能大赛——中药学类专业学生知识技能大赛',
      '全国大学生化工实验大赛',
      '中国高校地理科学展示大赛',
      '全国大学生GIS应用技能大赛',
      '全国大学生土地利用规划技能大赛',
      '全国大学生自然资源科技作品大赛',
      '全国高校地理师范生教学技能大赛',
      '"共享杯"大学生科技资源共享服务创新大赛',
      '全国大学生结构设计信息技术大赛',
      '全国高校BIM毕业设计大赛',
      '清华大学中国公共政策案例分析大赛',
      '"河仁杯"全国大学生社会调查技能大赛',
      '"淮海杯"全国高校模拟法庭大赛',
      '国际刑事法院模拟法庭(英文)',
      '"理律杯"全国高校模拟法庭竞赛',
      '杰赛普国际法模拟法庭大赛',
      '"iTeach"全国大学生数字化教育应用创新大赛',
      '全国高校学前(幼儿)教育专业优秀毕业论文奖',
      '全国大学生统计建模大赛',
      '全国高等师范院校历史学专业本科生教学技能比赛',
      '"发现中国"李济考古学奖学金',
      '全国大学生口述史成果交流赛',
      '全国大学生红色旅游创意策划大赛',
      'NextIdea腾讯新文创大赛',
      '全国大学生世界遗产保护与可持续利用提案大赛',
      'HRU大学生人力资源职业技能大赛',
      '全国大学生人力资源管理知识技能竞赛',
      '全国大学生物流仿真设计大赛',
      '中华全国日语演讲比赛',
      '中国日报社"21世纪杯"全国英语演讲比赛',
      '全国口译大赛',
      '全国高校俄语大赛',
      '全国大学生英语竞赛',
      '中国大学生广告艺术节学院奖',
      '全国大学生网络编辑创新大赛',
      '全国普通高等学校音乐学(教师教育)本科专业学生基本功大赛',
      '"为中国而设计"全国环境艺术设计大展',
      '全国大学生艺术展演活动',
      '中国大学生武术锦标赛',
      '国际武术大赛及武术节',
      '全国大学生运动会-各单项奖',
      '全国大学生联赛-各类联赛',
      '全国大学生锦标赛-各单项奖',
      // 省级竞赛
      '河南省高等学校师范教育专业毕业生教学技能比赛',
      '河南省大学生职业生涯规划大赛',
      '河南省大学生物理实验竞赛',
      '河南省大学生建筑模型大赛',
      '中南地区高校土木工程专业结构力学竞赛',
      '河南省翻译竞赛',
      '"漾翅杯"法律实践能力大赛',
      '"卓越杯"法治辩论赛',
      '河南省高校社会工作案例大赛',
      '"求是杯"全国公共管理案例大赛',
      '中国数据新闻大赛',
      '河南省大学生武术锦标赛',
      '北京电影学院动画学院奖',
      '河南高校摄影大赛',
      '河南省"新人新作"摄影展',
      '新光奖·中国西安国际原创动漫大赛',
      // 校级活动
      '河南大学辩论赛',
      '河南大学"我身边的创新创业典型"选拔赛',
      '河南大学"就业之星"求职应聘风采大赛',
      '"品时事、鉴青春"时政评论大赛',
      '"信仰之旅"活动大赛',
      '"阅读经典•\'典\'亮人生"征文大赛',
      '大学生学"习"论坛',
      '河南大学"我和我的祖国"朗诵大赛',
      '河南大学弘扬社会主义核心价值观短剧大赛',
      '河南大学英语文化节系列竞赛',
      '河南大学口腔技能竞赛',
      '河南大学大学生物理实验竞赛',
      '河南大学电子组装与设计大赛',
      '河南大学基础医学模型大赛',
      '河南大学健康素养大赛',
      '河南大学人体解剖学知识竞赛',
      '河南大学旅游路线设计大赛',
      '"书香•追梦——跟着习总书记学读书"系列活动',
      'iCAN大学生创新创业大赛',
      '"工行杯"全国大学生金融科技创新大赛',
      '"外教社杯"全国高校学生跨文化能力大赛',
      '全国大学生工业设计大赛',
      '全国大学生水利创新设计大赛',
      '全国大学生化学实验创新设计大赛',
      '全国大学生花园设计建造竞赛',
      '全国大学生测绘学科创新创业智能大赛',
      '全国大学生能源经济学术创意大赛',
      '全国大学生基础医学创新研究暨实验设计论坛（大赛）',
      '全国大学生数字媒体科技作品及创意竞赛',
      '全国本科院校税收风险管控案例大赛',
      '全国企业竞争模拟大赛',
      '全国高等院校数智化企业经营沙盘大赛',
      '全国数字建筑创新应用大赛',
      '"科云杯"全国大学生财会职业能力大赛',
      '"尖烽时刻"酒店管理模拟大赛',
      '全国大学生交通运输科技大赛',
      'UIA霍普杯国际大学生建筑设计竞赛',
      '全国师范院校师范生教学技能竞赛',
      '"法理争鸣"高校版权辩论赛',
      '河南省汉字大赛',
      '全国高校商业精英挑战赛——品牌策划竞赛',
      '全国高校商业精英挑战赛——会展专业创新创业实践竞赛',
      '全国高校商业精英挑战赛——国际贸易竞赛',
      '全国高校商业精英挑战赛——创新创业竞赛',
      '中国好创意暨全国数字艺术设计大赛',
      '全国三维数字化创新设计大赛',
      '"学创杯"全国大学生创业综合模拟大赛',
      '"大唐杯"全国大学生移动通信5G技术大赛',
      '全国大学生嵌入式芯片与系统设计竞赛',
      '全国高校体育教育专业学生基本功大赛',
      '中国大学生体育舞蹈锦标赛（区赛、总决赛）',
      '全国大学生混凝土材料设计大赛',
      '全国高等院校建筑与环境设计专业学生美术作品大奖赛',
      '齐越朗诵艺术节暨全国大学生朗诵大会',
      '中华经典诵写讲大赛',
      '大学生科技文化艺术节',
      '中国合唱节',
      '全国高校德语专业大学生德语辩论赛',
      '"儒易杯"中华文化国际翻译大赛',
      '河南省学生体育舞蹈锦标赛',
      '紫金奖——建筑及环境设计大赛',
      '黄河戏剧奖',
      '河南省合唱节',
      '铁塔记者文化节（新闻采写大赛）'
    ],
    level_options: [
      { value: 'national_1', label: '国家级一等奖' },
      { value: 'national_2', label: '国家级二等奖' },
      { value: 'national_3', label: '国家级三等奖' },
      { value: 'provincial_1', label: '省级一等奖' },
      { value: 'provincial_2', label: '省级二等奖' },
      { value: 'provincial_3', label: '省级三等奖' },
      { value: 'school_1', label: '校级一等奖' },
      { value: 'school_2', label: '校级二等奖' },
      { value: 'school_3', label: '校级三等奖' },
      { value: 'college_1', label: '院级一等奖' },
      { value: 'college_2', label: '院级二等奖' },
      { value: 'college_3', label: '院级三等奖' }
    ]
  },
  {
    id: 8,
    category_name: '英语六级',
    parent_id: null,
    max_score: 20,
    type: 'sports_arts',
    requires_certificate_name: false,
    requires_team_info: false,
    level_options: [
      { value: 'cet6_excellent', label: '六级优秀(≥550分)' },
      { value: 'cet6_good', label: '六级良好(500-549分)' },
      { value: 'cet6_pass', label: '六级通过(425-499分)' }
    ]
  },
  {
    id: 9,
    category_name: '学生干部',
    parent_id: null,
    max_score: 25,
    type: 'moral',
    requires_certificate_name: false,
    requires_team_info: false,
    level_options: [
      { value: 'school_president', label: '校级以上学生组织主席团成员' },
      { value: 'college_president', label: '院学生会主席团成员/青协主席团成员' },
      { value: 'grade_leader', label: '年级长（团支书）' },
      { value: 'class_leader', label: '班长（团支书）' },
      { value: 'department_head', label: '年级委员/班委成员' },
      { value: 'society_president', label: '各社团主席团成员' },
      { value: 'student_union_minister', label: '校院学生会部长' },
      { value: 'society_minister', label: '各社团部长' },
      { value: 'funding_captain', label: '院资助站队长（副队长）' },
      { value: 'dormitory_head', label: '寝室长' },
      { value: 'teaching_liaison', label: '教学联络员' }
    ]
  },
  {
    id: 10,
    category_name: '社会实践志愿服务',
    parent_id: null,
    max_score: 25,
    type: 'moral',
    requires_certificate_name: true,
    requires_team_info: false,
    level_options: [
      { value: 'national_individual', label: '国家级先进个人/优秀志愿者' },
      { value: 'provincial_individual', label: '省级先进个人/优秀志愿者' },
      { value: 'school_individual', label: '校级先进个人/优秀志愿者' },
      { value: 'college_individual', label: '院级先进个人/优秀志愿者' },
      { value: 'national_key_team', label: '国家级重点团队成员' },
      { value: 'provincial_key_team', label: '省级重点团队成员' },
      { value: 'school_key_team', label: '校级重点团队成员' },
      { value: 'college_key_team', label: '院级重点团队成员' },
      { value: 'national_normal_team', label: '国家级非重点团队成员' },
      { value: 'provincial_normal_team', label: '省级非重点团队成员' },
      { value: 'school_normal_team', label: '校级非重点团队成员' },
      { value: 'college_normal_team', label: '院级非重点团队成员' }
    ]
  },
  {
    id: 11,
    category_name: '新闻采编写',
    parent_id: null,
    max_score: 25,
    type: 'moral',
    requires_certificate_name: true,
    requires_team_info: false,
    level_options: [
      { value: 'national_media', label: '国家级媒体' },
      { value: 'provincial_media', label: '省级媒体' },
      { value: 'city_media', label: '市级媒体' },
      { value: 'school_media', label: '校级媒体' }
    ]
  },
  {
    id: 12,
    category_name: '见义勇为等表彰',
    parent_id: null,
    max_score: 25,
    type: 'moral',
    requires_certificate_name: true,
    requires_team_info: false,
    level_options: [
      { value: 'national', label: '国家级表彰' },
      { value: 'provincial', label: '省级表彰' },
      { value: 'city', label: '市级表彰' },
      { value: 'school', label: '校级表彰' }
    ]
  }
];

// JWT工具函数
const generateToken = (user) => {
  const payload = {
    id: user.id,
    student_id: user.student_id,
    name: user.name,
    role: user.role,
    permissions: user.permissions || [],
    is_super_admin: user.is_super_admin || false,
    iat: Math.floor(Date.now() / 1000),
    jti: Math.random().toString(36).substring(2) // JWT ID，用于撤销
  };

  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: '24h', // 延长过期时间到24小时，方便正常使用
    issuer: 'student-assessment-system',
    audience: 'student-assessment-users'
  });
};

// 认证中间件
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ message: '未提供认证令牌' });
  }

  // 检查令牌是否在黑名单中
  if (tokenBlacklist.has(token)) {
    return res.status(403).json({ message: '令牌已失效' });
  }

  jwt.verify(token, JWT_SECRET, {
    issuer: 'student-assessment-system',
    audience: 'student-assessment-users'
  }, (err, tokenUser) => {
    if (err) {
      console.log('🚨 令牌验证失败:', {
        error: err.message,
        ip: req.ip,
        userAgent: req.get('User-Agent')?.substring(0, 50)
      });

      // 记录令牌验证失败
      logOperation(null, '令牌验证失败', {
        error: err.message,
        ip: req.ip,
        user_agent: req.get('User-Agent')?.substring(0, 100)
      }, 'security', null, req);

      return res.status(403).json({ message: '令牌无效或已过期' });
    }

    // 从数据库重新获取用户信息，确保权限是最新的
    const currentUser = users.find(u => u.id === tokenUser.id);
    if (!currentUser) {
      console.log('🚨 令牌中的用户不存在:', {
        userId: tokenUser.id,
        ip: req.ip
      });

      return res.status(403).json({ message: '用户不存在' });
    }

    // 检查用户状态
    if (currentUser.status === 'disabled') {
      return res.status(403).json({ message: '账户已被禁用' });
    }

    req.user = currentUser;
    req.token = token; // 保存令牌用于可能的撤销
    next();
  });
};

// 学生权限中间件
const requireStudent = (req, res, next) => {
  if (req.user.role !== 'student') {
    return res.status(403).json({ message: '需要学生权限' });
  }
  next();
};

// 通用输入验证函数
const validateAndSanitizeInput = (input, maxLength = 100) => {
  if (!input || typeof input !== 'string') return '';

  // 检查危险模式
  if (!isValidInput(input)) {
    throw new Error('输入包含非法字符');
  }

  return sanitizeInput(input).substring(0, maxLength);
};

const validateNumericInput = (input, min = 0, max = Number.MAX_SAFE_INTEGER) => {
  const num = parseInt(input);
  if (isNaN(num) || num < min || num > max) {
    throw new Error('数值输入无效');
  }
  return num;
};

const validateEmailInput = (email) => {
  if (!email || typeof email !== 'string') return '';

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const cleanEmail = sanitizeInput(email);

  if (!emailRegex.test(cleanEmail)) {
    throw new Error('邮箱格式无效');
  }

  return cleanEmail;
};

// 教师权限中间件
const requireTeacher = (req, res, next) => {
  if (req.user.role !== 'teacher') {
    // 记录越权尝试
    logOperation(req.user.id, '越权访问尝试', {
      required_role: 'teacher',
      user_role: req.user.role,
      path: req.path,
      ip: req.ip
    }, 'security', null, req);

    return res.status(403).json({ message: '需要教师权限' });
  }
  next();
};

// 权限检查中间件
const requirePermission = (permission) => {
  return (req, res, next) => {
    const user = req.user;

    // 超级管理员拥有所有权限
    if (user.role === 'teacher' && user.is_super_admin) {
      return next();
    }

    // 检查用户是否有指定权限
    if (user.role === 'teacher' && user.permissions && user.permissions.includes(permission)) {
      return next();
    }

    return res.status(403).json({
      message: '权限不足',
      required_permission: permission
    });
  };
};

// 超级管理员权限检查
const requireSuperAdmin = (req, res, next) => {
  if (req.user.role !== 'teacher' || !req.user.is_super_admin) {
    return res.status(403).json({ message: '需要超级管理员权限' });
  }
  next();
};

// 按照细则计算证书分数
function calculateCertificateScore(cert) {
  const category = categories.find(cat => cat.id === cert.category_id);
  if (!category) return 0;

  let baseScore = 0;
  const level = cert.level;
  const teamSize = cert.team_size || 1;
  const role = cert.role || 'individual';

  // 根据证书类别和等级计算基础分数（严格按照细则）
  switch (category.category_name) {
    case '学科竞赛':
      // 学科竞赛分值：国家级12/10/8，省级8/7/6，校级6/5/4，院级4/3/2
      const academicScores = {
        'national_1': 12, 'national_2': 10, 'national_3': 8,
        'provincial_1': 8, 'provincial_2': 7, 'provincial_3': 6,
        'school_1': 6, 'school_2': 5, 'school_3': 4,
        'college_1': 4, 'college_2': 3, 'college_3': 2
      };
      baseScore = academicScores[level] || 0;
      // 学科竞赛不进行团队分值调整
      break;

    case '实践技能类竞赛':
      // 实践技能类竞赛分值：国家级10/9/8，省级8/7/6，校级6/5/4，院级4/3/2
      const practicalScores = {
        'national_1': 10, 'national_2': 9, 'national_3': 8,
        'provincial_1': 8, 'provincial_2': 7, 'provincial_3': 6,
        'school_1': 6, 'school_2': 5, 'school_3': 4,
        'college_1': 4, 'college_2': 3, 'college_3': 2
      };
      baseScore = practicalScores[level] || 0;
      // 团队分值调整：2-3人团队除以2，4人以上团队除以3
      if (teamSize >= 2 && teamSize <= 3) {
        baseScore = Math.round(baseScore / 2 * 10) / 10;
      } else if (teamSize >= 4) {
        baseScore = Math.round(baseScore / 3 * 10) / 10;
      }
      break;

    case '学生创新型项目':
      // 学生创新型项目：区分主持人和成员
      const innovationScores = {
        // 主持人分值
        'national_1_leader': 10, 'national_2_leader': 9, 'national_3_leader': 8,
        'provincial_1_leader': 8, 'provincial_2_leader': 7, 'provincial_3_leader': 6,
        'school_1_leader': 6, 'school_2_leader': 5, 'school_3_leader': 4,
        'college_1_leader': 4, 'college_2_leader': 3, 'college_3_leader': 2,
        // 成员分值
        'national_1_member': 5, 'national_2_member': 4.5, 'national_3_member': 4,
        'provincial_1_member': 4, 'provincial_2_member': 3.5, 'provincial_3_member': 3,
        'school_1_member': 3, 'school_2_member': 2.5, 'school_3_member': 2,
        'college_1_member': 2, 'college_2_member': 1.5, 'college_3_member': 1
      };
      const innovationKey = `${level}_${role === 'leader' ? 'leader' : 'member'}`;
      baseScore = innovationScores[innovationKey] || 0;
      break;

    case '专业认证':
      // 专业认证：高级8分，中级6分，初级2分
      const certScores = {
        'professional_high': 8,    // 高级认证
        'professional_mid': 6,     // 中级认证
        'professional_basic': 2,   // 初级认证
        'high_level': 8,           // 兼容旧格式
        'mid_level': 6,            // 兼容旧格式
        'basic_level': 2           // 兼容旧格式
      };
      baseScore = certScores[level] || 0;
      break;

    case '科学研究':
      // 科学研究：核心期刊6分，发明专利发明人15分，参与者5分
      const researchScores = {
        'core_journal': 6,        // 核心期刊论文
        'patent_inventor': 15,    // 发明专利发明人
        'patent_participant': 5   // 发明专利参与者（前三名）
      };
      baseScore = researchScores[level] || 0;
      break;

    case '体育竞赛':
      // 体育竞赛：按名次计分，国家级前三名10/8/7分，其他6分
      const sportsScores = {
        'national_1': 10, 'national_2': 8, 'national_3': 7, 'national_other': 6,
        'provincial_1': 7, 'provincial_2': 6, 'provincial_3': 5, 'provincial_other': 4,
        'school_1': 5, 'school_2': 4, 'school_3': 3, 'school_other': 2,
        'college_1': 3, 'college_2': 2, 'college_3': 1, 'college_other': 0
      };
      baseScore = sportsScores[level] || 0;
      // 团队分值调整：2-3人团队除以2，4人以上团队除以3
      if (teamSize >= 2 && teamSize <= 3) {
        baseScore = Math.round(baseScore / 2 * 10) / 10;
      } else if (teamSize >= 4) {
        baseScore = Math.round(baseScore / 3 * 10) / 10;
      }
      break;

    case '文艺活动':
      // 文艺活动：国家级10/8/7，省级7/6/5，校级5/4/3，院级3/2/1
      const artsScores = {
        'national_1': 10, 'national_2': 8, 'national_3': 7,
        'provincial_1': 7, 'provincial_2': 6, 'provincial_3': 5,
        'school_1': 5, 'school_2': 4, 'school_3': 3,
        'college_1': 3, 'college_2': 2, 'college_3': 1
      };
      baseScore = artsScores[level] || 0;
      // 团队分值调整：2-3人团队除以2，4人以上团队除以3
      if (teamSize >= 2 && teamSize <= 3) {
        baseScore = Math.round(baseScore / 2 * 10) / 10;
      } else if (teamSize >= 4) {
        baseScore = Math.round(baseScore / 3 * 10) / 10;
      }
      break;

    case '英语六级':
      // 英语六级：固定6分
      baseScore = 6;
      break;

    case '学生干部':
      // 学生干部分值：按照细则详细分类
      const leadershipScores = {
        // 10分：校级以上学生组织主席团成员
        'school_president': 10,

        // 8分：院学生会主席团成员、青协主席团成员、年级长（团支书）、班长（团支书）
        'college_president': 8,
        'grade_leader': 8,
        'class_leader': 8,

        // 6分：年级委员、班委成员、各社团主席团成员、校院学生会部长、各社团部长、院资助站队长（副队长）
        'department_head': 6,
        'society_president': 6,
        'student_union_minister': 6,
        'society_minister': 6,
        'funding_captain': 6,

        // 3分：寝室长、教学联络员
        'dormitory_head': 3,
        'teaching_liaison': 3
      };
      baseScore = leadershipScores[level] || 0;
      break;

    case '社会实践志愿服务':
      // 社会实践志愿服务：国家级先进个人10分，省级8分，校级5分，院级3分
      const volunteerScores = {
        'national_individual': 10,     // 国家级先进个人、优秀志愿者
        'provincial_individual': 8,    // 省级先进个人、优秀志愿者
        'school_individual': 5,        // 校级先进个人、优秀志愿者
        'college_individual': 3,       // 院级先进个人、优秀志愿者
        'national_key_team': 8,        // 国家级重点团队成员
        'provincial_key_team': 6,      // 省级重点团队成员
        'school_key_team': 4,          // 校级重点团队成员
        'college_key_team': 2,         // 院级重点团队成员
        'national_normal_team': 4,     // 国家级非重点团队成员
        'provincial_normal_team': 3,   // 省级非重点团队成员
        'school_normal_team': 2,       // 校级非重点团队成员
        'college_normal_team': 1       // 院级非重点团队成员
      };
      baseScore = volunteerScores[level] || 0;
      break;

    case '新闻采编写':
      // 新闻采编写：每次0.1分
      baseScore = 0.1;
      break;

    case '见义勇为等表彰':
      // 见义勇为等表彰：根据级别5/4/3分
      const honorScores = {
        'national_honor': 5,    // 国家级表彰
        'provincial_honor': 4,  // 省级表彰
        'school_honor': 3,      // 校级表彰
        'national': 5,          // 兼容旧格式
        'provincial': 4,        // 兼容旧格式
        'city': 3,              // 兼容旧格式
        'school': 3             // 兼容旧格式
      };
      baseScore = honorScores[level] || 0;
      break;

    default:
      baseScore = 0;
  }

  return Math.round(baseScore * 10) / 10; // 保留一位小数
}

// 成绩计算函数（按照细则）
function calculateScore(userCertificates) {
  const scores = {
    // 基础分值
    base_score: 25,

    // 专业类活动详细分类
    academic_competition: 0,      // 学科竞赛
    practical_skills: 0,          // 实践技能类竞赛
    innovation_project: 0,        // 学生创新型项目
    professional_cert: 0,         // 专业认证
    research: 0,                  // 科学研究
    professional_total: 0,        // 专业类总分

    // 体育美育类详细分类
    sports_competition: 0,        // 体育竞赛
    arts_activity: 0,             // 文艺活动
    english_cet6: 0,              // 英语六级
    sports_arts_total: 0,         // 体育美育总分

    // 文明品德类详细分类
    student_leader: 0,            // 学生干部
    volunteer_service: 0,         // 社会实践志愿服务
    news_writing: 0,              // 新闻采编写
    moral_honor: 0,               // 见义勇为等表彰
    moral_total: 0,               // 文明品德总分

    // 总分和最终成绩
    total: 25,
    final: 0
  };

  // 重新计算所有证书分数
  const certificatesWithScores = userCertificates.map(cert => ({
    ...cert,
    calculated_score: calculateCertificateScore(cert)
  }));

  // 按类别分组证书
  const certsByType = {
    professional: [],
    sports_arts: [],
    moral: []
  };

  certificatesWithScores.forEach(cert => {
    const category = categories.find(cat => cat.id === cert.category_id);
    if (category) {
      certsByType[category.type].push(cert);
    }
  });

  // 计算各类别分数（按照细则限制）
  Object.keys(certsByType).forEach(type => {
    const certs = certsByType[type];
    if (certs.length > 0) {
      if (type === 'sports_arts') {
        // 体育美育类分数计算
        const sportsCerts = certs.filter(c => {
          const category = categories.find(cat => cat.id === c.category_id);
          return category && category.category_name === '体育竞赛';
        });
        const artsCerts = certs.filter(c => {
          const category = categories.find(cat => cat.id === c.category_id);
          return category && category.category_name === '文艺活动';
        });
        const englishCerts = certs.filter(c => {
          const category = categories.find(cat => cat.id === c.category_id);
          return category && category.category_name === '英语六级';
        });

        // 体育竞赛：每人限报2项，满分10分
        const topSportsCerts = sportsCerts
          .sort((a, b) => b.calculated_score - a.calculated_score)
          .slice(0, 2);
        scores.sports_competition = Math.min(topSportsCerts.reduce((sum, cert) => sum + cert.calculated_score, 0), 10);

        // 文艺活动：每人限报2项，满分10分
        const topArtsCerts = artsCerts
          .sort((a, b) => b.calculated_score - a.calculated_score)
          .slice(0, 2);
        scores.arts_activity = Math.min(topArtsCerts.reduce((sum, cert) => sum + cert.calculated_score, 0), 10);

        // 英语六级：固定6分
        scores.english_cet6 = englishCerts.length > 0 ? 6 : 0;

        // 体育美育总分上限20分
        scores.sports_arts_total = Math.min(scores.sports_competition + scores.arts_activity + scores.english_cet6, 20);
      } else if (type === 'moral') {
        // 文明品德类：各子类有不同限制
        const leadershipCerts = certs.filter(c => {
          const category = categories.find(cat => cat.id === c.category_id);
          return category && category.category_name === '学生干部';
        });
        const volunteerCerts = certs.filter(c => {
          const category = categories.find(cat => cat.id === c.category_id);
          return category && category.category_name === '社会实践志愿服务';
        });
        const newsCerts = certs.filter(c => {
          const category = categories.find(cat => cat.id === c.category_id);
          return category && category.category_name === '新闻采编写';
        });
        const honorCerts = certs.filter(c => {
          const category = categories.find(cat => cat.id === c.category_id);
          return category && category.category_name === '见义勇为等表彰';
        });

        // 学生干部：按最高分值，不累计
        scores.student_leader = leadershipCerts.length > 0 ?
          Math.max(...leadershipCerts.map(c => c.calculated_score)) : 0;

        // 社会实践志愿服务：满分10分
        scores.volunteer_service = Math.min(
          volunteerCerts.sort((a, b) => b.calculated_score - a.calculated_score)
            .reduce((sum, cert) => sum + cert.calculated_score, 0), 10
        );

        // 新闻采编写：每次0.1分，上限2分
        scores.news_writing = Math.min(
          newsCerts.reduce((sum, cert) => sum + cert.calculated_score, 0), 2
        );

        // 见义勇为等表彰
        scores.moral_honor = honorCerts.reduce((sum, cert) => sum + cert.calculated_score, 0);

        // 文明品德总分上限25分
        scores.moral_total = Math.min(scores.student_leader + scores.volunteer_service + scores.news_writing + scores.moral_honor, 25);
      } else if (type === 'professional') {
        // 专业类分数计算
        const academicCerts = certs.filter(c => {
          const category = categories.find(cat => cat.id === c.category_id);
          return category && category.category_name === '学科竞赛';
        });
        const practicalCerts = certs.filter(c => {
          const category = categories.find(cat => cat.id === c.category_id);
          return category && category.category_name === '实践技能类竞赛';
        });
        const innovationCerts = certs.filter(c => {
          const category = categories.find(cat => cat.id === c.category_id);
          return category && category.category_name === '学生创新型项目';
        });
        const certificationCerts = certs.filter(c => {
          const category = categories.find(cat => cat.id === c.category_id);
          return category && category.category_name === '专业认证';
        });
        const researchCerts = certs.filter(c => {
          const category = categories.find(cat => cat.id === c.category_id);
          return category && category.category_name === '科学研究';
        });

        // 学科竞赛：累计加分，无特殊限制
        scores.academic_competition = academicCerts.reduce((sum, cert) => sum + cert.calculated_score, 0);

        // 实践技能类竞赛：累计加分，无特殊限制
        scores.practical_skills = practicalCerts.reduce((sum, cert) => sum + cert.calculated_score, 0);

        // 学生创新型项目：累计加分，无特殊限制
        scores.innovation_project = innovationCerts.reduce((sum, cert) => sum + cert.calculated_score, 0);

        // 专业认证：累计加分
        scores.professional_cert = certificationCerts.reduce((sum, cert) => sum + cert.calculated_score, 0);

        // 科学研究：每人限报2篇
        const topResearchCerts = researchCerts
          .sort((a, b) => b.calculated_score - a.calculated_score)
          .slice(0, 2);
        scores.research = topResearchCerts.reduce((sum, cert) => sum + cert.calculated_score, 0);

        // 专业类总分上限30分
        scores.professional_total = Math.min(
          scores.academic_competition + scores.practical_skills + scores.innovation_project + scores.professional_cert + scores.research,
          30
        );
      }
    }
  });

  // 计算总分
  scores.total = scores.base_score + scores.professional_total + scores.sports_arts_total + scores.moral_total;
  scores.final = Math.round(scores.total * 0.2 * 100) / 100; // 保留两位小数

  return scores;
}



// API路由

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'SQLite演示模式运行中' });
});



// 安全工具函数
const sanitizeInput = (input) => {
  if (typeof input !== 'string') return '';
  // 移除潜在的SQL注入字符和脚本
  return input
    .replace(/[<>'"`;\\]/g, '') // 移除危险字符
    .replace(/\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b/gi, '') // 移除SQL关键字
    .trim()
    .substring(0, 100); // 限制长度
};

const isValidInput = (input) => {
  if (!input || typeof input !== 'string') return false;
  // 检查是否包含危险模式
  const dangerousPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b.*\b(FROM|INTO|SET|WHERE|VALUES)\b)/gi,
    /(1\s*=\s*1|1\s*=\s*'1'|'.*'.*=.*'.*')/gi,
    /(<script|javascript:|vbscript:|onload=|onerror=)/gi,
    /(\.\.\/|\.\.\\|\.\.\%2f|\.\.\%5c)/gi, // 路径遍历
    /(\|\||&&|\$\(|\`)/gi // 命令注入
  ];

  return !dangerousPatterns.some(pattern => pattern.test(input));
};

// 专门用于登录的更宽松的输入验证
const isValidLoginInput = (input) => {
  if (!input || typeof input !== 'string') return false;
  // 只检查最严重的安全威胁，允许更多正常字符
  const criticalPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b.*\b(FROM|INTO|SET|WHERE|VALUES)\b)/gi,
    /(<script|javascript:|vbscript:)/gi,
    /(\.\.\/|\.\.\\)/gi // 路径遍历
  ];

  return !criticalPatterns.some(pattern => pattern.test(input));
};

// 登录尝试限制
const loginAttempts = new Map();
const MAX_LOGIN_ATTEMPTS = 10; // 放宽到10次尝试
const LOCKOUT_TIME = 5 * 60 * 1000; // 缩短到5分钟

const checkLoginAttempts = (identifier) => {
  const attempts = loginAttempts.get(identifier);
  if (!attempts) return true;

  if (attempts.count >= MAX_LOGIN_ATTEMPTS) {
    const timeSinceLastAttempt = Date.now() - attempts.lastAttempt;
    if (timeSinceLastAttempt < LOCKOUT_TIME) {
      return false;
    } else {
      // 重置计数
      loginAttempts.delete(identifier);
      return true;
    }
  }
  return true;
};

const recordLoginAttempt = (identifier, success) => {
  if (success) {
    loginAttempts.delete(identifier);
    return;
  }

  const attempts = loginAttempts.get(identifier) || { count: 0, lastAttempt: 0 };
  attempts.count++;
  attempts.lastAttempt = Date.now();
  loginAttempts.set(identifier, attempts);
};

// 登录
app.post('/api/auth/login', async (req, res) => {
  try {
    const { student_id, password } = req.body;

    // 输入验证
    if (!student_id || !password) {
      return res.status(400).json({ message: '账号和密码不能为空' });
    }

    // 安全检查（使用更宽松的登录验证）
    if (!isValidLoginInput(student_id) || !isValidLoginInput(password)) {
      console.log('⚠️ 检测到潜在的安全威胁（使用宽松验证）:', {
        student_id: student_id?.substring(0, 10) + '...',
        ip: req.ip,
        userAgent: req.get('User-Agent')?.substring(0, 50)
      });

      // 记录安全事件
      logOperation(null, '安全威胁检测', {
        threat_type: 'input_validation_failed',
        input_sample: student_id?.substring(0, 20),
        ip: req.ip,
        user_agent: req.get('User-Agent')?.substring(0, 100)
      }, 'security', null, req);

      return res.status(400).json({ message: '输入包含严重安全威胁字符' });
    }

    // 清理输入
    const cleanStudentId = sanitizeInput(student_id);
    const cleanPassword = sanitizeInput(password);

    if (!cleanStudentId || !cleanPassword) {
      return res.status(400).json({ message: '账号和密码格式不正确' });
    }

    // 检查登录尝试限制
    const clientIdentifier = req.ip + '_' + cleanStudentId;
    if (!checkLoginAttempts(clientIdentifier)) {
      console.log('🚨 登录尝试过多，账户被锁定:', {
        student_id: cleanStudentId,
        ip: req.ip
      });

      // 记录暴力破解尝试
      logOperation(null, '暴力破解检测', {
        target_account: cleanStudentId,
        ip: req.ip,
        user_agent: req.get('User-Agent')?.substring(0, 100)
      }, 'security', null, req);

      return res.status(429).json({
        message: '登录尝试过多，请15分钟后再试'
      });
    }

    // 支持学号或用户名登录
    const user = users.find(u => u.student_id === cleanStudentId || u.username === cleanStudentId);
    if (!user) {
      console.log('用户不存在:', cleanStudentId);
      recordLoginAttempt(clientIdentifier, false);
      return res.status(401).json({ message: '账号或密码错误' });
    }

    if (cleanPassword === user.password) {
      // 登录成功，重置尝试计数
      recordLoginAttempt(clientIdentifier, true);

    const token = generateToken(user);

    // 更新最后登录时间
    user.last_login = new Date().toISOString();
    saveData();

    // 记录登录日志
    logOperation(user.id, '用户登录', {
      login_method: 'password',
      user_role: user.role,
      user_name: user.name
    }, 'auth', null, req);

    res.json({
      message: '登录成功',
      token,
      user: {
        id: user.id,
        student_id: user.student_id,
        username: user.username,
        name: user.name,
        class_name: user.class_name,
        role: user.role,
        permissions: user.permissions || [],
        is_super_admin: user.is_super_admin || false
      }
    });
    } else {
      // 密码错误
      console.log('密码错误:', cleanStudentId);
      recordLoginAttempt(clientIdentifier, false);

      // 记录登录失败
      logOperation(user.id, '登录失败', {
        reason: 'wrong_password',
        ip: req.ip,
        user_agent: req.get('User-Agent')?.substring(0, 100)
      }, 'security', null, req);

      return res.status(401).json({ message: '账号或密码错误' });
    }
  } catch (error) {
    console.error('登录处理错误:', error);

    // 记录系统错误
    logOperation(null, '登录系统错误', {
      error: error.message,
      ip: req.ip
    }, 'system', null, req);

    return res.status(500).json({ message: '系统错误，请稍后重试' });
  }
});

// 获取当前用户信息
app.get('/api/auth/me', authenticateToken, (req, res) => {
  const user = users.find(u => u.id === req.user.id);
  if (user) {
    res.json({
      data: {
        user: {
          id: user.id,
          student_id: user.student_id,
          username: user.username,
          name: user.name,
          class_name: user.class_name,
          role: user.role,
          permissions: user.permissions || [],
          is_super_admin: user.is_super_admin || false
        }
      }
    });
  } else {
    res.status(404).json({ message: '用户不存在' });
  }
});

// 安全登出
app.post('/api/auth/logout', authenticateToken, (req, res) => {
  try {
    const token = req.token;

    // 将令牌加入黑名单
    tokenBlacklist.add(token);

    // 记录登出日志
    logOperation(req.user.id, '用户登出', {
      logout_method: 'manual',
      ip: req.ip,
      user_agent: req.get('User-Agent')?.substring(0, 100)
    }, 'auth', null, req);

    res.json({ message: '登出成功' });
  } catch (error) {
    console.error('登出处理错误:', error);
    res.status(500).json({ message: '登出失败' });
  }
});

// 撤销所有令牌（强制登出所有设备）
app.post('/api/auth/revoke-all', authenticateToken, (req, res) => {
  try {
    // 记录强制登出日志
    logOperation(req.user.id, '强制登出所有设备', {
      ip: req.ip,
      user_agent: req.get('User-Agent')?.substring(0, 100)
    }, 'security', null, req);

    res.json({ message: '已撤销所有设备的登录状态' });
  } catch (error) {
    console.error('撤销令牌错误:', error);
    res.status(500).json({ message: '操作失败' });
  }
});



// 获取学生列表
app.get('/api/users/students', authenticateToken, (req, res) => {
  try {
    const { page = 1, limit = 10, search = '', class_name = '' } = req.query;

    // 获取所有学生用于统计
    const allStudents = users.filter(u => u.role === 'student');

    // 计算统计数据
    const statistics = {
      total: allStudents.length,
      withCertificates: allStudents.filter(s => s.certificate_count > 0).length,
      withScores: allStudents.filter(s => s.final_score > 0).length
    };

    let filteredUsers = allStudents;

    if (search) {
      filteredUsers = filteredUsers.filter(u =>
        u.name.includes(search) || u.student_id.includes(search)
      );
    }

    if (class_name) {
      filteredUsers = filteredUsers.filter(u => u.class_name === class_name);
    }

    const total = filteredUsers.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

    res.json({
      data: {
        students: paginatedUsers,
        total,
        statistics,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('获取学生列表失败:', error);
    res.status(500).json({ message: '获取学生列表失败' });
  }
});

// 获取班级列表
app.get('/api/users/classes', authenticateToken, (req, res) => {
  try {
    const classes = {};
    users.filter(u => u.role === 'student').forEach(user => {
      if (user.class_name) {
        if (!classes[user.class_name]) {
          classes[user.class_name] = 0;
        }
        classes[user.class_name]++;
      }
    });
    
    const classesArray = Object.keys(classes).map(className => ({
      class_name: className,
      student_count: classes[className]
    }));
    
    res.json({
      data: {
        classes: classesArray
      }
    });
  } catch (error) {
    console.error('获取班级列表失败:', error);
    res.status(500).json({ message: '获取班级列表失败' });
  }
});

// 获取我的成绩
app.get('/api/scores/my', authenticateToken, (req, res) => {
  try {
    // 只允许学生查看自己的成绩
    if (req.user.role !== 'student') {
      return res.status(403).json({ message: '需要学生权限' });
    }

    const { audit_activity_id = '' } = req.query;
    const userId = req.user.id;
    const user = users.find(u => u.id === userId);

    console.log('获取学生成绩:', user?.student_id, '活动ID:', audit_activity_id);

    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }

    // 根据审核活动筛选证书
    let userCertificates;
    if (audit_activity_id) {
      userCertificates = certificates.filter(c =>
        c.user_id === userId &&
        c.status === 'approved' &&
        c.audit_activity_id === parseInt(audit_activity_id)
      );
    } else {
      userCertificates = certificates.filter(c => c.user_id === userId && c.status === 'approved');
    }

    // 优先使用手动修改的成绩，否则使用计算的成绩
    let scores;
    if (user.manual_scores && !audit_activity_id) {
      // 只有在查看全部成绩时才使用手动修改的成绩
      scores = user.manual_scores;
    } else {
      const calculatedScore = calculateScore(userCertificates);
      scores = calculatedScore; // 直接使用详细的分类分数
    }

    // 确保基础分值正确显示
    if (!scores.base_score) {
      scores.base_score = 25;
    }

    // 确保总分和最终成绩正确计算
    if (!scores.total || scores.total === 0) {
      scores.total = scores.base_score + (scores.professional_total || 0) + (scores.sports_arts_total || 0) + (scores.moral_total || 0);
    }

    if (!scores.final || scores.final === 0) {
      scores.final = Math.round(scores.total * 0.2 * 100) / 100;
    }

    // 添加证书详情
    const certificatesWithDetails = userCertificates.map(cert => {
      const category = categories.find(cat => cat.id === cert.category_id);
      return {
        ...cert,
        category_name: category ? category.category_name : '未知类别',
        category_type: category ? category.type : 'unknown'
      };
    });

    console.log('获取学生成绩:', req.user.student_id);

    // 确保字段名与前端一致
    const responseScore = {
      ...scores,
      // 兼容前端字段名
      final_score: scores.final,
      total_score: scores.total,
      professional_score: scores.professional_total || 0,
      sports_arts_score: scores.sports_arts_total || 0,
      moral_score: scores.moral_total || 0,
      base_score: scores.base_score || 25
    };

    res.json({
      data: {
        student: {
          id: user.id,
          student_id: user.student_id,
          name: user.name,
          class_name: user.class_name
        },
        score: responseScore,
        certificates: certificatesWithDetails,
        certificate_count: userCertificates.length
      }
    });
  } catch (error) {
    console.error('获取成绩失败:', error);
    res.status(500).json({ message: '获取成绩失败' });
  }
});

// 获取班级成绩
app.get('/api/scores/class', authenticateToken, (req, res) => {
  try {
    const { page = 1, limit = 10, class_name = '', audit_activity_id = '' } = req.query;

    let studentsWithScores = users.filter(u => u.role === 'student').map(user => {
      // 根据审核活动筛选证书
      let userCertificates;
      if (audit_activity_id) {
        userCertificates = certificates.filter(c =>
          c.user_id === user.id &&
          c.status === 'approved' &&
          c.audit_activity_id === parseInt(audit_activity_id)
        );
      } else {
        userCertificates = certificates.filter(c => c.user_id === user.id && c.status === 'approved');
      }

      // 优先使用手动修改的成绩，否则使用计算的成绩
      let scores;
      if (user.manual_scores && !audit_activity_id) {
        // 只有在查看全部成绩时才使用手动修改的成绩
        scores = user.manual_scores;
      } else {
        scores = calculateScore(userCertificates);
      }

      return {
        user_id: user.id,
        student_id: user.student_id,
        student_name: user.name,
        class_name: user.class_name,

        // 基础分值
        base_score: scores.base_score,

        // 专业类详细分类
        academic_competition: scores.academic_competition,
        practical_skills: scores.practical_skills,
        innovation_project: scores.innovation_project,
        professional_cert: scores.professional_cert,
        research: scores.research,
        professional_total: scores.professional_total,

        // 体育美育类详细分类
        sports_competition: scores.sports_competition,
        arts_activity: scores.arts_activity,
        english_cet6: scores.english_cet6,
        sports_arts_total: scores.sports_arts_total,

        // 文明品德类详细分类
        student_leader: scores.student_leader,
        volunteer_service: scores.volunteer_service,
        news_writing: scores.news_writing,
        moral_honor: scores.moral_honor,
        moral_total: scores.moral_total,

        // 总分和最终成绩
        total: scores.total,
        final: scores.final,

        // 兼容前端字段名
        total_score: scores.total,
        final_score: scores.final,
        professional_score: scores.professional_total,
        sports_arts_score: scores.sports_arts_total,
        moral_score: scores.moral_total,

        certificate_count: userCertificates.length,
        updated_at: scores.updated_at || new Date().toISOString()
      };
    });

    // 保存所有学生成绩用于统计
    const allScores = [...studentsWithScores];

    if (class_name) {
      studentsWithScores = studentsWithScores.filter(s => s.class_name === class_name);
    }

    const total = studentsWithScores.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedScores = studentsWithScores.slice(startIndex, endIndex);

    res.json({
      data: {
        scores: paginatedScores,
        all_scores: allScores, // 用于前端统计
        total,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('获取班级成绩失败:', error);
    res.status(500).json({ message: '获取班级成绩失败' });
  }
});

// 获取我的证书
app.get('/api/certificates', authenticateToken, (req, res) => {
  try {
    const userId = req.user.id;
    const { status, limit = 10 } = req.query;

    let userCertificates = certificates.filter(c => c.user_id === userId);

    if (status) {
      userCertificates = userCertificates.filter(c => c.status === status);
    }

    if (limit) {
      userCertificates = userCertificates.slice(0, parseInt(limit));
    }

    const certificatesWithCategory = userCertificates.map(cert => {
      const category = categories.find(cat => cat.id === cert.category_id);
      return {
        ...cert,
        category_name: category ? category.category_name : '未知类别'
      };
    });

    res.json({
      data: {
        certificates: certificatesWithCategory
      }
    });
  } catch (error) {
    console.error('获取证书列表失败:', error);
    res.status(500).json({ message: '获取证书列表失败' });
  }
});

// 获取待审核证书（教师用）
app.get('/api/certificates/pending', authenticateToken, (req, res) => {
  try {
    const { page = 1, limit = 10, status = '', category_id = '', search = '', audit_activity_id = '' } = req.query;

    console.log('获取待审核证书参数:', { page, limit, status, category_id, search, audit_activity_id });
    console.log('当前证书总数:', certificates.length);

    let filteredCertificates = certificates.map(cert => {
      const user = users.find(u => u.id === cert.user_id);
      const category = categories.find(cat => cat.id === cert.category_id);

      return {
        ...cert,
        student_name: user ? user.name : '未知学生',
        student_id: user ? user.student_id : '',
        class_name: user ? user.class_name : '',
        category_name: category ? category.category_name : '未知类别'
      };
    });

    if (status) {
      filteredCertificates = filteredCertificates.filter(c => c.status === status);
    }

    if (category_id) {
      filteredCertificates = filteredCertificates.filter(c => c.category_id === parseInt(category_id));
    }

    if (audit_activity_id && audit_activity_id !== 'null' && audit_activity_id !== 'undefined') {
      filteredCertificates = filteredCertificates.filter(c => c.audit_activity_id === parseInt(audit_activity_id));
    }

    if (search) {
      filteredCertificates = filteredCertificates.filter(c =>
        c.student_name.includes(search) || c.certificate_name.includes(search)
      );
    }

    const total = filteredCertificates.length;
    console.log('筛选后证书数量:', total);

    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedCertificates = filteredCertificates.slice(startIndex, endIndex);

    res.json({
      data: {
        certificates: paginatedCertificates,
        total,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('获取证书列表失败:', error);
    res.status(500).json({ message: '获取证书列表失败' });
  }
});

// 获取单个学生成绩详情
app.get('/api/scores/student/:id', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;
    const student = users.find(u => u.id === parseInt(id) && u.role === 'student');

    if (!student) {
      return res.status(404).json({ message: '学生不存在' });
    }

    const userCertificates = certificates.filter(c => c.user_id === parseInt(id) && c.status === 'approved');
    const score = calculateScore(userCertificates);

    // 添加证书详情
    const certificatesWithDetails = userCertificates.map(cert => {
      const category = categories.find(cat => cat.id === cert.category_id);
      return {
        ...cert,
        category_name: category ? category.category_name : '未知类别',
        category_type: category ? category.type : 'unknown'
      };
    });

    res.json({
      data: {
        student: {
          id: student.id,
          student_id: student.student_id,
          name: student.name,
          class_name: student.class_name
        },
        score: {
          base_score: 25,
          professional_score: score.professional,
          sports_arts_score: score.sports_arts,
          moral_score: score.moral,
          total_score: score.total,
          final_score: score.final
        },
        certificates: certificatesWithDetails
      }
    });
  } catch (error) {
    console.error('获取学生详情失败:', error);
    res.status(500).json({ message: '获取学生详情失败' });
  }
});

// 重新计算所有学生成绩
app.post('/api/scores/recalculate', authenticateToken, requireTeacher, (req, res) => {
  try {
    let recalculatedCount = 0;

    // 重新计算所有学生的成绩
    const studentsWithScores = users.filter(u => u.role === 'student').map(user => {
      const userCertificates = certificates.filter(c => c.user_id === user.id && c.status === 'approved');
      const score = calculateScore(userCertificates);
      recalculatedCount++;

      return {
        user_id: user.id,
        student_id: user.student_id,
        student_name: user.name,
        class_name: user.class_name,
        base_score: 25,
        professional_score: score.professional,
        sports_arts_score: score.sports_arts,
        moral_score: score.moral,
        total_score: score.total,
        final_score: score.final,
        certificate_count: userCertificates.length,
        updated_at: new Date().toISOString()
      };
    });

    console.log('重新计算成绩:', recalculatedCount, '个学生');

    res.json({
      message: `成功重新计算 ${recalculatedCount} 个学生的成绩`,
      data: {
        recalculated_count: recalculatedCount,
        scores: studentsWithScores
      }
    });
  } catch (error) {
    console.error('重新计算成绩失败:', error);
    res.status(500).json({ message: '重新计算成绩失败' });
  }
});

// 修改学生成绩
app.put('/api/scores/student/:id', authenticateToken, requireTeacher, (req, res) => {
  try {
    const { id } = req.params;
    const { base_score, professional_score, sports_arts_score, moral_score } = req.body;

    const student = users.find(u => u.id === parseInt(id) && u.role === 'student');
    if (!student) {
      return res.status(404).json({ message: '学生不存在' });
    }

    // 验证分值范围
    if (base_score < 0 || base_score > 25) {
      return res.status(400).json({ message: '基础分值范围0-25分' });
    }
    if (professional_score < 0 || professional_score > 30) {
      return res.status(400).json({ message: '专业类活动分值范围0-30分' });
    }
    if (sports_arts_score < 0 || sports_arts_score > 20) {
      return res.status(400).json({ message: '体育美育分值范围0-20分' });
    }
    if (moral_score < 0 || moral_score > 25) {
      return res.status(400).json({ message: '文明品德分值范围0-25分' });
    }

    // 更新学生成绩（这里简化处理，实际应该存储在数据库中）
    student.manual_scores = {
      base_score: parseFloat(base_score),
      professional_score: parseFloat(professional_score),
      sports_arts_score: parseFloat(sports_arts_score),
      moral_score: parseFloat(moral_score),
      total_score: parseFloat(base_score) + parseFloat(professional_score) + parseFloat(sports_arts_score) + parseFloat(moral_score),
      final_score: (parseFloat(base_score) + parseFloat(professional_score) + parseFloat(sports_arts_score) + parseFloat(moral_score)) * 0.2,
      updated_at: new Date().toISOString(),
      updated_by: req.user.id
    };

    console.log('修改学生成绩:', student.name, student.student_id);

    res.json({
      message: '成绩修改成功',
      data: {
        student: {
          id: student.id,
          student_id: student.student_id,
          name: student.name,
          class_name: student.class_name
        },
        scores: student.manual_scores
      }
    });
  } catch (error) {
    console.error('修改学生成绩失败:', error);
    res.status(500).json({ message: '修改学生成绩失败' });
  }
});

// 批量修改学生成绩
app.put('/api/scores/batch', authenticateToken, requireTeacher, (req, res) => {
  try {
    const { student_ids, base_score_adjustment, professional_score_adjustment, sports_arts_score_adjustment, moral_score_adjustment } = req.body;

    if (!student_ids || !Array.isArray(student_ids) || student_ids.length === 0) {
      return res.status(400).json({ message: '请选择要修改的学生' });
    }

    let updatedCount = 0;
    const errors = [];

    student_ids.forEach(studentId => {
      const student = users.find(u => u.id === parseInt(studentId) && u.role === 'student');
      if (!student) {
        errors.push(`学生ID ${studentId} 不存在`);
        return;
      }

      // 获取当前成绩
      const currentScores = student.manual_scores || {
        base_score: 25,
        professional_score: 0,
        sports_arts_score: 0,
        moral_score: 0
      };

      // 应用调整
      const newScores = {
        base_score: Math.max(0, Math.min(25, currentScores.base_score + (base_score_adjustment || 0))),
        professional_score: Math.max(0, Math.min(30, currentScores.professional_score + (professional_score_adjustment || 0))),
        sports_arts_score: Math.max(0, Math.min(20, currentScores.sports_arts_score + (sports_arts_score_adjustment || 0))),
        moral_score: Math.max(0, Math.min(25, currentScores.moral_score + (moral_score_adjustment || 0)))
      };

      newScores.total_score = newScores.base_score + newScores.professional_score + newScores.sports_arts_score + newScores.moral_score;
      newScores.final_score = newScores.total_score * 0.2;
      newScores.updated_at = new Date().toISOString();
      newScores.updated_by = req.user.id;

      student.manual_scores = newScores;
      updatedCount++;
    });

    console.log('批量修改成绩:', updatedCount, '个学生');

    res.json({
      message: `成功修改 ${updatedCount} 个学生的成绩${errors.length > 0 ? `，${errors.length} 个失败` : ''}`,
      data: {
        updated_count: updatedCount,
        error_count: errors.length,
        errors: errors
      }
    });
  } catch (error) {
    console.error('批量修改成绩失败:', error);
    res.status(500).json({ message: '批量修改成绩失败' });
  }
});

// 获取证书类别
app.get('/api/categories', (req, res) => {
  try {
    res.json({
      data: {
        categories: categories,
        flat_categories: categories
      }
    });
  } catch (error) {
    console.error('获取类别失败:', error);
    res.status(500).json({ message: '获取类别失败' });
  }
});

// 删除单个学生
app.delete('/api/users/students/:id', authenticateToken, requireTeacher, (req, res) => {
  try {
    const { id } = req.params;
    const studentIndex = users.findIndex(u => u.id === parseInt(id) && u.role === 'student');

    if (studentIndex === -1) {
      return res.status(404).json({ message: '学生不存在' });
    }

    const deletedStudent = users[studentIndex];
    users.splice(studentIndex, 1);

    // 同时删除该学生的所有证书
    const deletedCertificatesCount = certificates.filter(c => c.user_id === parseInt(id)).length;
    certificates = certificates.filter(c => c.user_id !== parseInt(id));
    saveData(); // 保存数据

    // 记录删除学生操作
    logOperation(req.user.id, '删除学生', {
      student_id: deletedStudent.student_id,
      student_name: deletedStudent.name,
      class_name: deletedStudent.class_name,
      deleted_certificates_count: deletedCertificatesCount
    }, 'student', deletedStudent.id, req);

    console.log('删除学生:', deletedStudent.name, deletedStudent.student_id);
    res.json({
      message: '学生删除成功',
      data: { deleted_student: deletedStudent }
    });
  } catch (error) {
    console.error('删除学生失败:', error);
    res.status(500).json({ message: '删除学生失败' });
  }
});

// 批量删除学生
app.post('/api/users/students/batch-delete', authenticateToken, requireTeacher, (req, res) => {
  try {
    const { student_ids } = req.body;

    if (!Array.isArray(student_ids) || student_ids.length === 0) {
      return res.status(400).json({ message: '请提供要删除的学生ID列表' });
    }

    const deletedStudents = [];
    const notFoundIds = [];

    student_ids.forEach(id => {
      const studentIndex = users.findIndex(u => u.id === parseInt(id) && u.role === 'student');
      if (studentIndex !== -1) {
        const deletedStudent = users[studentIndex];
        users.splice(studentIndex, 1);
        deletedStudents.push(deletedStudent);

        // 删除该学生的所有证书
        certificates = certificates.filter(c => c.user_id !== parseInt(id));
      } else {
        notFoundIds.push(id);
      }
    });

    if (deletedStudents.length > 0) {
      saveData(); // 保存数据
    }

    console.log('批量删除学生:', deletedStudents.length, '个');
    res.json({
      message: `成功删除 ${deletedStudents.length} 个学生`,
      data: {
        deleted_count: deletedStudents.length,
        deleted_students: deletedStudents,
        not_found_ids: notFoundIds
      }
    });
  } catch (error) {
    console.error('批量删除学生失败:', error);
    res.status(500).json({ message: '批量删除学生失败' });
  }
});

// 清空所有学生
app.post('/api/users/students/clear-all', authenticateToken, requireTeacher, (req, res) => {
  try {
    // 获取所有学生
    const allStudents = users.filter(u => u.role === 'student');
    const studentCount = allStudents.length;

    if (studentCount === 0) {
      return res.json({
        message: '没有学生需要删除',
        data: { deleted_count: 0 }
      });
    }

    // 获取所有学生ID
    const studentIds = allStudents.map(s => s.id);

    // 删除所有学生
    users = users.filter(u => u.role !== 'student');

    // 删除所有学生的证书
    certificates = certificates.filter(c => !studentIds.includes(c.user_id));

    console.log('清空所有学生:', studentCount, '个');
    res.json({
      message: `成功删除所有 ${studentCount} 个学生`,
      data: {
        deleted_count: studentCount,
        deleted_students: allStudents
      }
    });
  } catch (error) {
    console.error('清空所有学生失败:', error);
    res.status(500).json({ message: '清空所有学生失败' });
  }
});

// 添加学生
app.post('/api/users/students', authenticateToken, requireTeacher, (req, res) => {
  try {
    const { student_id, name, class_name, password } = req.body;

    if (!student_id || !name || !class_name) {
      return res.status(400).json({ message: '学号、姓名、班级为必填项' });
    }

    // 检查学号是否已存在
    const existingUser = users.find(u => u.student_id === student_id);
    if (existingUser) {
      return res.status(400).json({ message: '学号已存在' });
    }

    const newStudent = {
      id: nextUserId++,
      student_id,
      name,
      class_name,
      password: password || student_id, // 默认密码为学号
      role: 'student'
    };

    users.push(newStudent);
    saveData(); // 保存数据

    // 记录添加学生操作
    logOperation(req.user.id, '添加学生', {
      student_id: newStudent.student_id,
      student_name: newStudent.name,
      class_name: newStudent.class_name
    }, 'student', newStudent.id, req);

    console.log('添加学生:', newStudent.name, newStudent.student_id);
    res.json({
      message: '学生添加成功',
      data: { student: newStudent }
    });
  } catch (error) {
    console.error('添加学生失败:', error);
    res.status(500).json({ message: '添加学生失败' });
  }
});

// 修改学生信息
app.put('/api/users/students/:id', authenticateToken, requireTeacher, (req, res) => {
  try {
    const { id } = req.params;
    const { student_id, name, class_name, password } = req.body;

    const studentIndex = users.findIndex(u => u.id === parseInt(id) && u.role === 'student');
    if (studentIndex === -1) {
      return res.status(404).json({ message: '学生不存在' });
    }

    // 检查学号是否与其他学生冲突
    if (student_id) {
      const existingUser = users.find(u => u.student_id === student_id && u.id !== parseInt(id));
      if (existingUser) {
        return res.status(400).json({ message: '学号已存在' });
      }
    }

    const student = users[studentIndex];
    if (student_id) student.student_id = student_id;
    if (name) student.name = name;
    if (class_name) student.class_name = class_name;
    if (password) student.password = password;

    console.log('修改学生信息:', student.name, student.student_id);
    res.json({
      message: '学生信息修改成功',
      data: { student }
    });
  } catch (error) {
    console.error('修改学生信息失败:', error);
    res.status(500).json({ message: '修改学生信息失败' });
  }
});

// 重置学生密码
app.put('/api/users/students/:id/reset-password', authenticateToken, requireTeacher, (req, res) => {
  try {
    const { id } = req.params;
    const student = users.find(u => u.id === parseInt(id) && u.role === 'student');

    if (!student) {
      return res.status(404).json({ message: '学生不存在' });
    }

    student.password = student.student_id; // 重置为学号

    console.log('重置密码:', student.name, student.student_id);
    res.json({
      message: '密码重置成功，新密码为学号',
      data: { student_id: student.student_id }
    });
  } catch (error) {
    console.error('重置密码失败:', error);
    res.status(500).json({ message: '重置密码失败' });
  }
});

// 获取个人证书列表
app.get('/api/certificates/my', authenticateToken, (req, res) => {
  try {
    // 只允许学生访问自己的证书
    if (req.user.role !== 'student') {
      return res.status(403).json({ message: '需要学生权限' });
    }

    const { status, limit = 1000, audit_activity_id } = req.query;

    let userCertificates = certificates.filter(c => c.user_id === req.user.id);

    // 按状态筛选
    if (status) {
      userCertificates = userCertificates.filter(c => c.status === status);
    }

    // 按审核活动筛选
    if (audit_activity_id) {
      userCertificates = userCertificates.filter(c => c.audit_activity_id === parseInt(audit_activity_id));
    }

    // 限制数量
    userCertificates = userCertificates.slice(0, parseInt(limit));

    // 添加证书类别信息
    const certificatesWithDetails = userCertificates.map(cert => {
      const category = categories.find(cat => cat.id === cert.category_id);
      return {
        ...cert,
        category_name: category ? category.category_name : '未知类别',
        category_type: category ? category.type : 'unknown'
      };
    });

    console.log('获取个人证书:', req.user.student_id, '活动ID:', audit_activity_id, '数量:', certificatesWithDetails.length);

    res.json({
      data: {
        certificates: certificatesWithDetails,
        total: certificatesWithDetails.length
      }
    });
  } catch (error) {
    console.error('获取个人证书失败:', error);
    res.status(500).json({ message: '获取个人证书失败' });
  }
});

// 提交证书
app.post('/api/certificates', authenticateToken, (req, res) => {
  // 使用自定义的 multer 中间件来处理错误
  upload.single('file')(req, res, (err) => {
    if (err) {
      console.error('文件上传错误:', err);
      if (err.code === 'UNEXPECTED_FIELD') {
        return res.status(400).json({ message: '文件字段名错误，期望字段名为 "file"' });
      }
      return res.status(400).json({ message: '文件上传失败: ' + err.message });
    }

    // 继续处理证书提交逻辑
    handleCertificateSubmission(req, res);
  });
});

// 证书提交处理函数
function handleCertificateSubmission(req, res) {
  try {
    // 只允许学生提交证书
    if (req.user.role !== 'student') {
      return res.status(403).json({ message: '需要学生权限' });
    }

    // 检查证书提交是否开启
    if (!systemSettings.certificate_submission_enabled) {
      return res.status(400).json({ message: '证书提交功能已关闭' });
    }

    // 检查提交时间限制
    const now = new Date();
    if (systemSettings.certificate_submission_start) {
      const startDate = new Date(systemSettings.certificate_submission_start);
      if (now < startDate) {
        return res.status(400).json({ message: '证书提交尚未开始' });
      }
    }
    if (systemSettings.certificate_submission_deadline) {
      const deadline = new Date(systemSettings.certificate_submission_deadline);
      if (now > deadline) {
        return res.status(400).json({ message: '证书提交已截止' });
      }
    }

    const { category_id, certificate_name, level, award_date, team_size, role, audit_activity_id } = req.body;

    if (!category_id || !level) {
      return res.status(400).json({ message: '证书类别、获奖等级为必填项' });
    }

    if (!audit_activity_id) {
      return res.status(400).json({ message: '请选择审核活动' });
    }

    // 验证审核活动是否存在且有效
    const auditActivity = auditActivities.find(a => a.id === parseInt(audit_activity_id));
    if (!auditActivity) {
      return res.status(400).json({ message: '审核活动不存在' });
    }

    if (auditActivity.status !== 'active') {
      return res.status(400).json({ message: '审核活动已关闭' });
    }

    // 检查审核活动时间（结束日期包含当天23:59:59）
    if (auditActivity.end_date && new Date(auditActivity.end_date + 'T23:59:59') < now) {
      return res.status(400).json({ message: '审核活动已结束' });
    }

    const category = categories.find(cat => cat.id === parseInt(category_id));
    if (!category) {
      return res.status(400).json({ message: '证书类别不存在' });
    }

    // 检查是否需要证书名称
    if ((category.requires_certificate_name || category.use_predefined_names) && !certificate_name) {
      const fieldName = category.use_predefined_names ? '竞赛名称' : '证书名称';
      return res.status(400).json({ message: `该类别需要${category.use_predefined_names ? '选择' : '填写'}${fieldName}` });
    }



    // 检查该类别在当前审核活动中已提交的证书数量（按照细则限制）
    const userCertsInCategory = certificates.filter(c =>
      c.user_id === req.user.id &&
      c.category_id === parseInt(category_id) &&
      c.audit_activity_id === parseInt(audit_activity_id)
    );

    // 根据证书类别检查提交限制
    let maxSubmissions = null; // null表示无限制
    if (category.category_name === '体育竞赛' || category.category_name === '文艺活动') {
      maxSubmissions = 2; // 体育竞赛和文艺活动每人限报2项
    } else if (category.category_name === '科学研究') {
      maxSubmissions = 2; // 学术论文每人限报2篇
    } else if (category.category_name === '学生干部') {
      maxSubmissions = 1; // 学生干部每人限报1项
    } else if (category.category_name === '英语六级') {
      maxSubmissions = 1; // 英语六级每人限报1项
    }

    console.log(`提交限制检查 - 类别: ${category.category_name}, 已提交数量: ${userCertsInCategory.length}, 最大限制: ${maxSubmissions}`);

    if (maxSubmissions && userCertsInCategory.length >= maxSubmissions) {
      console.log(`提交被拒绝 - 超出限制: ${category.category_name}`);
      return res.status(400).json({
        message: `在当前审核活动中，${category.category_name}类别最多只能提交${maxSubmissions}个证书`
      });
    }

    // 检查同一项目是否已经在当前审核活动中提交（同一项目只能作为一类加分项计算）
    const sameProjectCerts = certificates.filter(c =>
      c.user_id === req.user.id &&
      c.certificate_name === certificate_name &&
      c.award_date === (award_date || new Date().toISOString().split('T')[0]) &&
      c.audit_activity_id === parseInt(audit_activity_id)
    );

    if (sameProjectCerts.length > 0) {
      return res.status(400).json({
        message: '在当前审核活动中，同一项目只能作为一类加分项计算，请勿重复提交'
      });
    }



    // 检查是否为自定义活动名称
    const isCustomActivity = category.predefined_names &&
      !category.predefined_names.includes(certificate_name);

    // 根据细则自动计算分值
    const calculatedScore = calculateCertificateScore({
      category_id: parseInt(category_id),
      level,
      team_size: parseInt(team_size) || 1,
      role: role || 'individual'
    });

    const newCertificate = {
      id: nextCertId++,
      user_id: req.user.id,
      category_id: parseInt(category_id),
      certificate_name,
      level,
      score: calculatedScore, // 提交时就按细则计算分值
      award_date: award_date || new Date().toISOString().split('T')[0],
      team_size: parseInt(team_size) || 1,
      role: role || 'individual',
      file_path: req.file ? req.file.filename : null,
      audit_activity_id: parseInt(audit_activity_id), // 关联审核活动
      is_custom_activity: isCustomActivity, // 标记是否为自定义活动
      status: 'pending',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    certificates.push(newCertificate);
    saveData(); // 保存数据

    console.log('提交证书:', newCertificate.certificate_name, '用户:', req.user.student_id);

    res.json({
      message: '证书提交成功，等待审核',
      data: { certificate: newCertificate }
    });
  } catch (error) {
    console.error('提交证书失败:', error);
    res.status(500).json({ message: '提交证书失败' });
  }
}

// 批量提交证书
app.post('/api/certificates/batch', authenticateToken, upload.array('files', 10), (req, res) => {
  try {
    // 只允许学生提交证书
    if (req.user.role !== 'student') {
      return res.status(403).json({ message: '需要学生权限' });
    }

    // 检查证书提交是否开启
    if (!systemSettings.certificate_submission_enabled) {
      return res.status(400).json({ message: '证书提交功能已关闭' });
    }

    // 检查提交时间限制
    const now = new Date();
    if (systemSettings.certificate_submission_start) {
      const startDate = new Date(systemSettings.certificate_submission_start);
      if (now < startDate) {
        return res.status(400).json({ message: '证书提交尚未开始' });
      }
    }
    if (systemSettings.certificate_submission_deadline) {
      const deadline = new Date(systemSettings.certificate_submission_deadline);
      if (now > deadline) {
        return res.status(400).json({ message: '证书提交已截止' });
      }
    }

    const { certificates: certificatesData } = req.body;

    if (!certificatesData) {
      return res.status(400).json({ message: '证书数据不能为空' });
    }

    let parsedCertificates;
    try {
      parsedCertificates = JSON.parse(certificatesData);
    } catch (e) {
      return res.status(400).json({ message: '证书数据格式错误' });
    }

    if (!Array.isArray(parsedCertificates) || parsedCertificates.length === 0) {
      return res.status(400).json({ message: '至少需要提交一个证书' });
    }

    const newCertificates = [];
    const errors = [];

    parsedCertificates.forEach((certData, index) => {
      try {
        const { category_id, certificate_name, level, award_date, team_size, role, audit_activity_id } = certData;

        if (!category_id || !certificate_name || !level) {
          errors.push(`第${index + 1}个证书：证书类别、证书名称、获奖等级为必填项`);
          return;
        }

        if (!audit_activity_id) {
          errors.push(`第${index + 1}个证书：请选择审核活动`);
          return;
        }

        const category = categories.find(cat => cat.id === parseInt(category_id));
        if (!category) {
          errors.push(`第${index + 1}个证书：证书类别不存在`);
          return;
        }

        // 验证审核活动是否存在且有效
        const auditActivity = auditActivities.find(a => a.id === parseInt(audit_activity_id));
        if (!auditActivity) {
          errors.push(`第${index + 1}个证书：审核活动不存在`);
          return;
        }

        if (auditActivity.status !== 'active') {
          errors.push(`第${index + 1}个证书：审核活动已关闭`);
          return;
        }

        // 检查该类别在当前审核活动中已提交的证书数量（包括本次批量提交中的证书）
        const userCertsInCategory = certificates.filter(c =>
          c.user_id === req.user.id &&
          c.category_id === parseInt(category_id) &&
          c.audit_activity_id === parseInt(audit_activity_id)
        );

        // 计算本次批量提交中同类别的证书数量
        const currentBatchSameCategoryCount = newCertificates.filter(c =>
          c.category_id === parseInt(category_id) &&
          c.audit_activity_id === parseInt(audit_activity_id)
        ).length;

        // 根据证书类别检查提交限制
        let maxSubmissions = null; // null表示无限制
        if (category.category_name === '体育竞赛' || category.category_name === '文艺活动') {
          maxSubmissions = 2; // 体育竞赛和文艺活动每人限报2项
        } else if (category.category_name === '科学研究') {
          maxSubmissions = 2; // 学术论文每人限报2篇
        } else if (category.category_name === '学生干部') {
          maxSubmissions = 1; // 学生干部每人限报1项
        } else if (category.category_name === '英语六级') {
          maxSubmissions = 1; // 英语六级每人限报1项
        }

        console.log(`批量提交限制检查 - 第${index + 1}个证书 - 类别: ${category.category_name}, 已提交数量: ${userCertsInCategory.length}, 本次批量中同类别: ${currentBatchSameCategoryCount}, 最大限制: ${maxSubmissions}`);

        if (maxSubmissions && (userCertsInCategory.length + currentBatchSameCategoryCount) >= maxSubmissions) {
          console.log(`批量提交被拒绝 - 第${index + 1}个证书超出限制: ${category.category_name}`);
          errors.push(`第${index + 1}个证书：在当前审核活动中，${category.category_name}类别最多只能提交${maxSubmissions}个证书`);
          return;
        }

        // 检查同一项目是否已经在当前审核活动中提交（同一项目只能作为一类加分项计算）
        const sameProjectCerts = certificates.filter(c =>
          c.user_id === req.user.id &&
          c.certificate_name === certificate_name &&
          c.award_date === (award_date || new Date().toISOString().split('T')[0]) &&
          c.audit_activity_id === parseInt(audit_activity_id)
        );

        // 检查本次批量提交中是否有重复项目
        const sameProjectInBatch = newCertificates.filter(c =>
          c.certificate_name === certificate_name &&
          c.award_date === (award_date || new Date().toISOString().split('T')[0]) &&
          c.audit_activity_id === parseInt(audit_activity_id)
        );

        if (sameProjectCerts.length > 0 || sameProjectInBatch.length > 0) {
          errors.push(`第${index + 1}个证书：在当前审核活动中，同一项目只能作为一类加分项计算，请勿重复提交`);
          return;
        }

        // 检查是否为自定义活动名称
        const isCustomActivity = category.predefined_names &&
          !category.predefined_names.includes(certificate_name);

        // 根据细则自动计算分值
        const calculatedScore = calculateCertificateScore({
          category_id: parseInt(category_id),
          level,
          team_size: parseInt(team_size) || 1,
          role: role || 'individual'
        });

        const newCertificate = {
          id: nextCertId++,
          user_id: req.user.id,
          category_id: parseInt(category_id),
          certificate_name,
          level,
          score: calculatedScore, // 提交时就按细则计算分值
          award_date: award_date || new Date().toISOString().split('T')[0],
          team_size: parseInt(team_size) || 1,
          role: role || 'individual',
          file_path: req.files && req.files[index] ? req.files[index].filename : null,
          audit_activity_id: parseInt(audit_activity_id), // 添加审核活动ID
          is_custom_activity: isCustomActivity, // 标记是否为自定义活动
          status: 'pending',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        certificates.push(newCertificate);
        newCertificates.push(newCertificate);

      } catch (error) {
        errors.push(`第${index + 1}个证书处理失败：${error.message}`);
      }
    });

    if (newCertificates.length > 0) {
      saveData(); // 保存数据
    }

    console.log('批量提交证书:', newCertificates.length, '个，用户:', req.user.student_id);

    res.json({
      message: `成功提交 ${newCertificates.length} 个证书${errors.length > 0 ? `，${errors.length} 个失败` : ''}`,
      data: {
        success_count: newCertificates.length,
        error_count: errors.length,
        certificates: newCertificates,
        errors: errors
      }
    });
  } catch (error) {
    console.error('批量提交证书失败:', error);
    res.status(500).json({ message: '批量提交证书失败' });
  }
});

// 删除证书
app.delete('/api/certificates/:id', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;
    let certificateIndex = -1;
    let certificate = null;

    if (req.user.role === 'student') {
      // 学生只能删除自己的证书
      certificateIndex = certificates.findIndex(c => c.id === parseInt(id) && c.user_id === req.user.id);

      if (certificateIndex === -1) {
        return res.status(404).json({ message: '证书不存在或无权限删除' });
      }

      certificate = certificates[certificateIndex];

      // 学生只能删除待审核或被拒绝的证书
      if (certificate.status === 'approved') {
        return res.status(400).json({ message: '已审核通过的证书不能删除' });
      }
    } else if (req.user.role === 'teacher') {
      // 教师可以删除任何证书
      certificateIndex = certificates.findIndex(c => c.id === parseInt(id));

      if (certificateIndex === -1) {
        return res.status(404).json({ message: '证书不存在' });
      }

      certificate = certificates[certificateIndex];
    } else {
      return res.status(403).json({ message: '无权限删除证书' });
    }

    // 获取证书所属用户信息
    const user = users.find(u => u.id === certificate.user_id);

    certificates.splice(certificateIndex, 1);
    saveData(); // 保存数据

    console.log('删除证书:', certificate.certificate_name, '操作者:', req.user.student_id, '证书所属:', user?.student_id);

    res.json({
      message: '证书删除成功',
      data: {
        deletedCertificate: certificate,
        deletedBy: req.user.student_id
      }
    });
  } catch (error) {
    console.error('删除证书失败:', error);
    res.status(500).json({ message: '删除证书失败' });
  }
});

// 批量删除证书
app.post('/api/certificates/batch-delete', authenticateToken, requireTeacher, (req, res) => {
  try {
    const { certificate_ids } = req.body;

    if (!Array.isArray(certificate_ids) || certificate_ids.length === 0) {
      return res.status(400).json({ message: '请提供要删除的证书ID列表' });
    }

    const deletedCertificates = [];
    const notFoundIds = [];

    certificate_ids.forEach(id => {
      const certificateIndex = certificates.findIndex(c => c.id === parseInt(id));

      if (certificateIndex !== -1) {
        const certificate = certificates[certificateIndex];
        const user = users.find(u => u.id === certificate.user_id);

        deletedCertificates.push({
          ...certificate,
          student_name: user?.name,
          student_id: user?.student_id
        });

        certificates.splice(certificateIndex, 1);
      } else {
        notFoundIds.push(id);
      }
    });

    saveData();

    console.log('批量删除证书:', deletedCertificates.length, '个，操作者:', req.user.student_id);

    res.json({
      message: `成功删除 ${deletedCertificates.length} 个证书`,
      data: {
        deletedCount: deletedCertificates.length,
        deletedCertificates,
        notFoundIds,
        deletedBy: req.user.student_id
      }
    });
  } catch (error) {
    console.error('批量删除证书失败:', error);
    res.status(500).json({ message: '批量删除证书失败' });
  }
});

// 获取单个证书详情
app.get('/api/certificates/:id', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;
    const certificate = certificates.find(c => c.id === parseInt(id));

    if (!certificate) {
      return res.status(404).json({ message: '证书不存在' });
    }

    // 检查权限：学生只能查看自己的证书，教师可以查看所有证书
    if (req.user.role === 'student' && certificate.user_id !== req.user.id) {
      return res.status(403).json({ message: '无权查看此证书' });
    }

    // 添加学生信息
    const user = users.find(u => u.id === certificate.user_id);
    const category = categories.find(c => c.id === certificate.category_id);

    const certificateWithDetails = {
      ...certificate,
      student_name: user?.name || '未知',
      student_id: user?.student_id || '未知',
      class_name: user?.class_name || '未知',
      category_name: category?.category_name || '未知'
    };

    res.json({
      success: true,
      data: certificateWithDetails
    });
  } catch (error) {
    console.error('获取证书详情失败:', error);
    res.status(500).json({ message: '获取证书详情失败' });
  }
});

// 审核证书
app.put('/api/certificates/:id/audit', authenticateToken, requireTeacher, (req, res) => {
  try {
    const { id } = req.params;
    const { action, score, comment } = req.body;

    if (!['approve', 'reject'].includes(action)) {
      return res.status(400).json({ message: '审核操作无效' });
    }

    const certificate = certificates.find(c => c.id === parseInt(id));
    if (!certificate) {
      return res.status(404).json({ message: '证书不存在' });
    }

    certificate.status = action === 'approve' ? 'approved' : 'rejected';

    // 审核通过时处理分数
    if (action === 'approve') {
      // 如果老师提供了分数，使用老师的分数；否则使用自动计算的分数
      if (score !== undefined && score !== null && score !== '') {
        certificate.score = parseFloat(score);
      } else {
        // 如果没有提供分数，使用自动计算的分数（证书提交时已计算）
        // 或者重新计算以确保准确性
        certificate.score = calculateCertificateScore(certificate);
      }
    } else {
      certificate.score = 0;
    }

    certificate.audit_comment = comment || '';
    certificate.audited_by = req.user.id;
    certificate.audited_at = new Date().toISOString();
    certificate.updated_at = new Date().toISOString();
    saveData(); // 保存数据

    // 记录审核操作
    const user = users.find(u => u.id === certificate.user_id);
    logOperation(req.user.id, `${action === 'approve' ? '审核通过' : '审核拒绝'}证书`, {
      certificate_id: certificate.id,
      certificate_name: certificate.certificate_name,
      student_name: user?.name,
      student_id: user?.student_id,
      score: certificate.score,
      comment: comment || '',
      auto_calculated_score: action === 'approve' ? certificate.score : null
    }, 'certificate', certificate.id, req);

    console.log('审核证书:', certificate.certificate_name, '状态:', certificate.status, '自动计算得分:', certificate.score);

    res.json({
      message: `证书${action === 'approve' ? '审核通过' : '审核拒绝'}`,
      data: { certificate }
    });
  } catch (error) {
    console.error('审核证书失败:', error);
    res.status(500).json({ message: '审核证书失败' });
  }
});

// 更新证书信息（教师权限）
app.put('/api/certificates/:id', authenticateToken, requireTeacher, (req, res) => {
  try {
    const { id } = req.params;
    const { category_id, certificate_name, level, award_date, team_size, role } = req.body;

    const certificate = certificates.find(c => c.id === parseInt(id));
    if (!certificate) {
      return res.status(404).json({ message: '证书不存在' });
    }

    // 更新证书信息
    if (category_id !== undefined) certificate.category_id = parseInt(category_id);
    if (certificate_name !== undefined) certificate.certificate_name = certificate_name;
    if (level !== undefined) certificate.level = level;
    if (award_date !== undefined) certificate.award_date = award_date;
    if (team_size !== undefined) certificate.team_size = parseInt(team_size) || 1;
    if (role !== undefined) certificate.role = role;

    certificate.updated_at = new Date().toISOString();
    certificate.modified_by = req.user.id;
    certificate.modified_at = new Date().toISOString();

    // 如果证书已审核通过，重新计算分数
    if (certificate.status === 'approved') {
      const newScore = calculateCertificateScore(certificate);
      certificate.score = newScore;
      console.log('重新计算证书分数:', certificate.certificate_name, '新分数:', newScore);
    }

    saveData(); // 保存数据

    console.log('更新证书信息:', certificate.certificate_name, '用户:', req.user.username);

    res.json({
      message: '证书信息更新成功',
      data: { certificate }
    });
  } catch (error) {
    console.error('更新证书信息失败:', error);
    res.status(500).json({ message: '更新证书信息失败' });
  }
});

// 审核活动管理API

// 获取审核活动列表
app.get('/api/audit-activities', authenticateToken, (req, res) => {
  try {
    // 为每个活动添加提交数量统计
    const activitiesWithStats = auditActivities.map(activity => ({
      ...activity,
      submission_count: certificates.filter(cert => cert.audit_activity_id === activity.id).length
    }));

    res.json({
      data: {
        activities: activitiesWithStats
      }
    });
  } catch (error) {
    console.error('获取审核活动列表失败:', error);
    res.status(500).json({ message: '获取审核活动列表失败' });
  }
});

// 创建审核活动
app.post('/api/audit-activities', authenticateToken, requireTeacher, (req, res) => {
  try {
    const { name, title, description, start_date, end_date, categories } = req.body;
    const activityName = name || title; // 兼容两种字段名

    if (!activityName || !start_date || !end_date) {
      return res.status(400).json({ message: '请填写完整的活动信息' });
    }

    const newActivity = {
      id: nextActivityId++,
      name: activityName,
      title: activityName, // 保持兼容性
      description: description || '',
      start_date,
      end_date,
      categories: categories || [], // 允许的证书类别
      status: 'active', // active, inactive
      created_by: req.user.id,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    auditActivities.push(newActivity);
    saveData();

    // 记录创建审核活动操作
    logOperation(req.user.id, '创建审核活动', {
      activity_name: newActivity.name,
      start_date: newActivity.start_date,
      end_date: newActivity.end_date,
      status: newActivity.status
    }, 'audit_activity', newActivity.id, req);

    console.log('创建审核活动:', title, '创建者:', req.user.username);

    res.json({
      message: '审核活动创建成功',
      data: { activity: newActivity }
    });
  } catch (error) {
    console.error('创建审核活动失败:', error);
    res.status(500).json({ message: '创建审核活动失败' });
  }
});

// 更新审核活动
app.put('/api/audit-activities/:id', authenticateToken, requireTeacher, (req, res) => {
  try {
    const { id } = req.params;
    const { name, title, description, start_date, end_date, categories, status } = req.body;
    const activityName = name || title; // 兼容两种字段名

    const activity = auditActivities.find(a => a.id === parseInt(id));
    if (!activity) {
      return res.status(404).json({ message: '审核活动不存在' });
    }

    if (activityName !== undefined) {
      activity.name = activityName;
      activity.title = activityName; // 保持兼容性
    }
    if (description !== undefined) activity.description = description;
    if (start_date !== undefined) activity.start_date = start_date;
    if (end_date !== undefined) activity.end_date = end_date;
    if (categories !== undefined) activity.categories = categories;
    if (status !== undefined) activity.status = status;

    activity.updated_at = new Date().toISOString();
    saveData();

    console.log('更新审核活动:', activity.title);

    res.json({
      message: '审核活动更新成功',
      data: { activity }
    });
  } catch (error) {
    console.error('更新审核活动失败:', error);
    res.status(500).json({ message: '更新审核活动失败' });
  }
});

// 重新计算所有已审核证书的分值
app.post('/api/admin/recalculate-scores', authenticateToken, requireTeacher, (req, res) => {
  try {
    const approvedCertificates = certificates.filter(cert => cert.status === 'approved');
    let updatedCount = 0;

    approvedCertificates.forEach(cert => {
      const oldScore = cert.score;
      const newScore = calculateCertificateScore(cert);

      if (oldScore !== newScore) {
        cert.score = newScore;
        cert.updated_at = new Date().toISOString();
        updatedCount++;
        console.log(`更新证书 ${cert.certificate_name} 分值: ${oldScore} -> ${newScore}`);
      }
    });

    if (updatedCount > 0) {
      saveData();
    }

    res.json({
      message: `重新计算完成，更新了 ${updatedCount} 个证书的分值`,
      data: {
        totalApproved: approvedCertificates.length,
        updatedCount: updatedCount
      }
    });
  } catch (error) {
    console.error('重新计算分值失败:', error);
    res.status(500).json({ message: '重新计算分值失败' });
  }
});

// 数据清理API - 清理无效证书
app.post('/api/admin/cleanup-certificates', authenticateToken, requireTeacher, (req, res) => {
  try {
    // 查找没有 audit_activity_id 的证书
    const invalidCertificates = certificates.filter(cert => !cert.audit_activity_id);

    if (invalidCertificates.length === 0) {
      return res.json({
        message: '没有发现无效的证书数据',
        data: {
          cleanedCount: 0,
          invalidCertificates: []
        }
      });
    }

    // 记录被清理的证书信息
    const cleanedCertificatesInfo = invalidCertificates.map(cert => {
      const user = users.find(u => u.id === cert.user_id);
      return {
        id: cert.id,
        certificate_name: cert.certificate_name,
        student_name: user?.name,
        student_id: user?.student_id,
        created_at: cert.created_at
      };
    });

    // 删除无效证书
    certificates = certificates.filter(cert => cert.audit_activity_id);
    saveData();

    console.log('清理无效证书:', invalidCertificates.length, '个');

    res.json({
      message: `成功清理 ${invalidCertificates.length} 个无效证书`,
      data: {
        cleanedCount: invalidCertificates.length,
        invalidCertificates: cleanedCertificatesInfo
      }
    });
  } catch (error) {
    console.error('清理证书数据失败:', error);
    res.status(500).json({ message: '清理证书数据失败' });
  }
});

// 管理员管理API

// 获取所有管理员
app.get('/api/administrators', authenticateToken, requireSuperAdmin, (req, res) => {
  try {
    const administrators = users.filter(u => u.role === 'teacher').map(admin => ({
      id: admin.id,
      username: admin.username,
      name: admin.name,
      is_super_admin: admin.is_super_admin || false,
      permissions: admin.permissions || [],
      created_at: admin.created_at,
      last_login: admin.last_login
    }));

    logOperation(req.user.id, '查看管理员列表', {}, 'admin', null, req);

    res.json({
      data: administrators
    });
  } catch (error) {
    console.error('获取管理员列表失败:', error);
    res.status(500).json({ message: '获取管理员列表失败' });
  }
});

// 创建管理员
app.post('/api/administrators', authenticateToken, requireSuperAdmin, (req, res) => {
  try {
    const { username, name, password, permissions } = req.body;

    if (!username || !name || !password) {
      return res.status(400).json({ message: '用户名、姓名、密码为必填项' });
    }

    // 检查用户名是否已存在
    const existingUser = users.find(u => u.username === username);
    if (existingUser) {
      return res.status(400).json({ message: '用户名已存在' });
    }

    // 验证权限
    const validPermissions = Object.values(PERMISSIONS);
    const invalidPermissions = permissions.filter(p => !validPermissions.includes(p));
    if (invalidPermissions.length > 0) {
      return res.status(400).json({
        message: '无效的权限',
        invalid_permissions: invalidPermissions
      });
    }

    const newAdmin = {
      id: nextUserId++,
      username,
      student_id: username, // 使用用户名作为登录ID
      name,
      password,
      role: 'teacher',
      is_super_admin: false,
      permissions: permissions || [],
      created_at: new Date().toISOString(),
      last_login: null
    };

    users.push(newAdmin);
    saveData();

    logOperation(req.user.id, '创建管理员', {
      target_username: username,
      target_name: name,
      assigned_permissions: permissions
    }, 'admin', newAdmin.id, req);

    res.json({
      message: '管理员创建成功',
      data: {
        id: newAdmin.id,
        username: newAdmin.username,
        name: newAdmin.name,
        permissions: newAdmin.permissions
      }
    });
  } catch (error) {
    console.error('创建管理员失败:', error);
    res.status(500).json({ message: '创建管理员失败' });
  }
});

// 更新管理员权限
app.put('/api/administrators/:id', authenticateToken, requireSuperAdmin, (req, res) => {
  try {
    const { id } = req.params;
    const { name, permissions } = req.body;

    const admin = users.find(u => u.id === parseInt(id) && u.role === 'teacher');
    if (!admin) {
      return res.status(404).json({ message: '管理员不存在' });
    }

    // 不能修改超级管理员
    if (admin.is_super_admin) {
      return res.status(400).json({ message: '不能修改超级管理员' });
    }

    // 验证权限
    if (permissions) {
      const validPermissions = Object.values(PERMISSIONS);
      const invalidPermissions = permissions.filter(p => !validPermissions.includes(p));
      if (invalidPermissions.length > 0) {
        return res.status(400).json({
          message: '无效的权限',
          invalid_permissions: invalidPermissions
        });
      }
    }

    const oldPermissions = admin.permissions || [];

    if (name) admin.name = name;
    if (permissions !== undefined) admin.permissions = permissions;

    saveData();

    logOperation(req.user.id, '更新管理员权限', {
      target_username: admin.username,
      target_name: admin.name,
      old_permissions: oldPermissions,
      new_permissions: permissions
    }, 'admin', admin.id, req);

    res.json({
      message: '管理员信息更新成功',
      data: {
        id: admin.id,
        username: admin.username,
        name: admin.name,
        permissions: admin.permissions
      }
    });
  } catch (error) {
    console.error('更新管理员失败:', error);
    res.status(500).json({ message: '更新管理员失败' });
  }
});

// 删除管理员
app.delete('/api/administrators/:id', authenticateToken, requireSuperAdmin, (req, res) => {
  try {
    const { id } = req.params;
    const adminIndex = users.findIndex(u => u.id === parseInt(id) && u.role === 'teacher');

    if (adminIndex === -1) {
      return res.status(404).json({ message: '管理员不存在' });
    }

    const admin = users[adminIndex];

    // 不能删除超级管理员
    if (admin.is_super_admin) {
      return res.status(400).json({ message: '不能删除超级管理员' });
    }

    // 不能删除自己
    if (admin.id === req.user.id) {
      return res.status(400).json({ message: '不能删除自己' });
    }

    users.splice(adminIndex, 1);
    saveData();

    logOperation(req.user.id, '删除管理员', {
      target_username: admin.username,
      target_name: admin.name
    }, 'admin', admin.id, req);

    res.json({
      message: '管理员删除成功',
      data: { deleted_admin: admin.username }
    });
  } catch (error) {
    console.error('删除管理员失败:', error);
    res.status(500).json({ message: '删除管理员失败' });
  }
});

// 重置管理员密码
app.post('/api/administrators/:id/reset-password', authenticateToken, requireSuperAdmin, (req, res) => {
  try {
    const { id } = req.params;
    const { new_password } = req.body;

    if (!new_password) {
      return res.status(400).json({ message: '请提供新密码' });
    }

    const admin = users.find(u => u.id === parseInt(id) && u.role === 'teacher');
    if (!admin) {
      return res.status(404).json({ message: '管理员不存在' });
    }

    // 不能重置超级管理员密码
    if (admin.is_super_admin && admin.id !== req.user.id) {
      return res.status(400).json({ message: '不能重置超级管理员密码' });
    }

    admin.password = new_password;
    saveData();

    logOperation(req.user.id, '重置管理员密码', {
      target_username: admin.username,
      target_name: admin.name
    }, 'admin', admin.id, req);

    res.json({
      message: '密码重置成功'
    });
  } catch (error) {
    console.error('重置密码失败:', error);
    res.status(500).json({ message: '重置密码失败' });
  }
});

// 操作日志API

// 获取操作日志
app.get('/api/operation-logs', authenticateToken, requireSuperAdmin, (req, res) => {
  try {
    const { page = 1, limit = 50, user_id, action, start_date, end_date } = req.query;

    let filteredLogs = [...operationLogs];

    // 按用户过滤
    if (user_id) {
      filteredLogs = filteredLogs.filter(log => log.user_id === parseInt(user_id));
    }

    // 按操作类型过滤
    if (action) {
      filteredLogs = filteredLogs.filter(log => log.action.includes(action));
    }

    // 按时间范围过滤
    if (start_date) {
      filteredLogs = filteredLogs.filter(log => new Date(log.created_at) >= new Date(start_date));
    }
    if (end_date) {
      filteredLogs = filteredLogs.filter(log => new Date(log.created_at) <= new Date(end_date));
    }

    // 按时间倒序排列
    filteredLogs.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

    // 分页
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedLogs = filteredLogs.slice(startIndex, endIndex);

    // 添加用户信息
    const logsWithUserInfo = paginatedLogs.map(log => {
      const user = users.find(u => u.id === log.user_id);
      return {
        ...log,
        user_name: user?.name || user?.username || 'Unknown',
        user_username: user?.username,
        user_student_id: user?.student_id
      };
    });

    res.json({
      data: {
        logs: logsWithUserInfo,
        total: filteredLogs.length,
        page: parseInt(page),
        limit: parseInt(limit),
        total_pages: Math.ceil(filteredLogs.length / limit)
      }
    });
  } catch (error) {
    console.error('获取操作日志失败:', error);
    res.status(500).json({ message: '获取操作日志失败' });
  }
});

// 获取权限列表
app.get('/api/permissions', authenticateToken, requireSuperAdmin, (req, res) => {
  try {
    const permissionList = Object.entries(PERMISSIONS).map(([key, value]) => ({
      key,
      value,
      name: getPermissionName(value)
    }));

    res.json({
      data: permissionList
    });
  } catch (error) {
    console.error('获取权限列表失败:', error);
    res.status(500).json({ message: '获取权限列表失败' });
  }
});

// 权限名称映射
function getPermissionName(permission) {
  const names = {
    [PERMISSIONS.CERTIFICATE_AUDIT]: '证书审核',
    [PERMISSIONS.STUDENT_MANAGEMENT]: '学生管理',
    [PERMISSIONS.GRADE_EXPORT]: '成绩导出',
    [PERMISSIONS.ACTIVITY_MANAGEMENT]: '审核活动管理',
    [PERMISSIONS.CATEGORY_MANAGEMENT]: '证书类别管理',
    [PERMISSIONS.ADMIN_MANAGEMENT]: '管理员管理',
    [PERMISSIONS.SYSTEM_SETTINGS]: '系统设置',
    [PERMISSIONS.DATA_CLEANUP]: '数据清理'
  };
  return names[permission] || permission;
}

// 系统统计API
app.get('/api/system/stats', authenticateToken, requireSuperAdmin, (req, res) => {
  try {
    const now = new Date();
    const today = now.toISOString().split('T')[0];
    const thisMonth = now.toISOString().substring(0, 7);

    // 用户统计
    const totalUsers = users.length;
    const totalStudents = users.filter(u => u.role === 'student').length;
    const totalAdmins = users.filter(u => u.role === 'teacher').length;

    // 证书统计
    const totalCertificates = certificates.length;
    const pendingCertificates = certificates.filter(c => c.status === 'pending').length;
    const approvedCertificates = certificates.filter(c => c.status === 'approved').length;
    const rejectedCertificates = certificates.filter(c => c.status === 'rejected').length;

    // 今日统计
    const todaySubmissions = certificates.filter(c =>
      c.created_at && c.created_at.startsWith(today)
    ).length;
    const todayLogins = operationLogs.filter(log =>
      log.action === '用户登录' && log.created_at.startsWith(today)
    ).length;

    // 本月统计
    const monthlySubmissions = certificates.filter(c =>
      c.created_at && c.created_at.startsWith(thisMonth)
    ).length;
    const monthlyLogins = operationLogs.filter(log =>
      log.action === '用户登录' && log.created_at.startsWith(thisMonth)
    ).length;

    // 审核活动统计
    const totalActivities = auditActivities.length;
    const activeActivities = auditActivities.filter(a => a.status === 'active').length;

    // 操作日志统计
    const totalLogs = operationLogs.length;
    const todayLogs = operationLogs.filter(log =>
      log.created_at.startsWith(today)
    ).length;

    // 最近活跃用户（最近7天登录的用户）
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
    const recentActiveUsers = operationLogs.filter(log =>
      log.action === '用户登录' && log.created_at >= sevenDaysAgo
    ).length;

    res.json({
      data: {
        users: {
          total: totalUsers,
          students: totalStudents,
          admins: totalAdmins,
          recentActive: recentActiveUsers
        },
        certificates: {
          total: totalCertificates,
          pending: pendingCertificates,
          approved: approvedCertificates,
          rejected: rejectedCertificates,
          todaySubmissions,
          monthlySubmissions
        },
        activities: {
          total: totalActivities,
          active: activeActivities
        },
        logs: {
          total: totalLogs,
          today: todayLogs
        },
        logins: {
          today: todayLogins,
          monthly: monthlyLogins
        },
        lastUpdated: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('获取系统统计失败:', error);
    res.status(500).json({ message: '获取系统统计失败' });
  }
});

// 删除操作日志
app.delete('/api/operation-logs/:id', authenticateToken, requireSuperAdmin, (req, res) => {
  try {
    const { id } = req.params;
    const logIndex = operationLogs.findIndex(log => log.id === parseInt(id));

    if (logIndex === -1) {
      return res.status(404).json({ message: '操作日志不存在' });
    }

    const deletedLog = operationLogs[logIndex];
    operationLogs.splice(logIndex, 1);
    saveData();

    // 记录删除日志的操作
    logOperation(req.user.id, '删除操作日志', {
      deleted_log_id: deletedLog.id,
      deleted_log_action: deletedLog.action,
      deleted_log_user_id: deletedLog.user_id
    }, 'operation_log', deletedLog.id, req);

    res.json({
      message: '操作日志删除成功',
      data: { deleted_log: deletedLog }
    });
  } catch (error) {
    console.error('删除操作日志失败:', error);
    res.status(500).json({ message: '删除操作日志失败' });
  }
});

// 批量删除操作日志
app.post('/api/operation-logs/batch-delete', authenticateToken, requireSuperAdmin, (req, res) => {
  try {
    const { log_ids, delete_type, before_date } = req.body;

    let deletedLogs = [];
    let deletedCount = 0;

    if (delete_type === 'selected' && log_ids && Array.isArray(log_ids)) {
      // 删除选中的日志
      log_ids.forEach(id => {
        const logIndex = operationLogs.findIndex(log => log.id === parseInt(id));
        if (logIndex !== -1) {
          deletedLogs.push(operationLogs[logIndex]);
          operationLogs.splice(logIndex, 1);
          deletedCount++;
        }
      });
    } else if (delete_type === 'before_date' && before_date) {
      // 删除指定日期之前的日志
      const cutoffDate = new Date(before_date);
      const logsToDelete = operationLogs.filter(log => new Date(log.created_at) < cutoffDate);

      deletedLogs = [...logsToDelete];
      operationLogs = operationLogs.filter(log => new Date(log.created_at) >= cutoffDate);
      deletedCount = deletedLogs.length;
    } else if (delete_type === 'all') {
      // 删除所有日志（除了当前操作）
      deletedLogs = [...operationLogs];
      operationLogs = [];
      deletedCount = deletedLogs.length;
    } else {
      return res.status(400).json({ message: '无效的删除类型或参数' });
    }

    saveData();

    // 记录批量删除操作
    logOperation(req.user.id, '批量删除操作日志', {
      delete_type,
      deleted_count: deletedCount,
      before_date: before_date || null
    }, 'operation_log', null, req);

    res.json({
      message: `成功删除 ${deletedCount} 条操作日志`,
      data: {
        deleted_count: deletedCount,
        deleted_logs: deletedLogs.slice(0, 10) // 只返回前10条作为示例
      }
    });
  } catch (error) {
    console.error('批量删除操作日志失败:', error);
    res.status(500).json({ message: '批量删除操作日志失败' });
  }
});

// 清空操作日志
app.post('/api/operation-logs/clear', authenticateToken, requireSuperAdmin, (req, res) => {
  try {
    const totalLogs = operationLogs.length;
    operationLogs = [];
    saveData();

    // 记录清空操作（这条记录会是唯一的记录）
    logOperation(req.user.id, '清空所有操作日志', {
      cleared_count: totalLogs
    }, 'operation_log', null, req);

    res.json({
      message: `成功清空 ${totalLogs} 条操作日志`,
      data: { cleared_count: totalLogs }
    });
  } catch (error) {
    console.error('清空操作日志失败:', error);
    res.status(500).json({ message: '清空操作日志失败' });
  }
});

// 删除审核活动
app.delete('/api/audit-activities/:id', authenticateToken, requireTeacher, (req, res) => {
  try {
    const { id } = req.params;
    const activityIndex = auditActivities.findIndex(a => a.id === parseInt(id));

    if (activityIndex === -1) {
      return res.status(404).json({ message: '审核活动不存在' });
    }

    const activity = auditActivities[activityIndex];

    // 删除与该活动相关的证书
    const relatedCertificates = certificates.filter(cert => cert.audit_activity_id === parseInt(id));

    // 记录被删除的证书信息（用于返回给前端）
    const deletedCertificatesInfo = relatedCertificates.map(cert => {
      const user = users.find(u => u.id === cert.user_id);
      return {
        id: cert.id,
        certificate_name: cert.certificate_name,
        student_name: user?.name,
        student_id: user?.student_id
      };
    });

    // 删除关联的证书
    certificates = certificates.filter(cert => cert.audit_activity_id !== parseInt(id));

    // 删除审核活动
    auditActivities.splice(activityIndex, 1);
    saveData();

    console.log('删除审核活动:', activity.title || activity.name, '同时删除相关证书:', relatedCertificates.length, '个');

    res.json({
      message: `审核活动删除成功，同时删除了 ${relatedCertificates.length} 个相关证书`,
      data: {
        deletedActivity: activity,
        deletedCertificatesCount: relatedCertificates.length,
        deletedCertificates: deletedCertificatesInfo
      }
    });
  } catch (error) {
    console.error('删除审核活动失败:', error);
    res.status(500).json({ message: '删除审核活动失败' });
  }
});

// 获取审核活动统计
app.get('/api/audit-activities/stats', authenticateToken, (req, res) => {
  try {
    const totalActivities = auditActivities.length;
    const activeActivities = auditActivities.filter(a => a.status === 'active').length;

    // 计算总提交数（所有审核活动相关的证书）
    const totalSubmissions = certificates.filter(cert =>
      auditActivities.some(activity => activity.id === cert.audit_activity_id)
    ).length;

    res.json({
      data: {
        totalActivities,
        activeActivities,
        totalSubmissions
      }
    });
  } catch (error) {
    console.error('获取审核活动统计失败:', error);
    res.status(500).json({ message: '获取统计数据失败' });
  }
});

// 系统设置API

// 获取系统设置
app.get('/api/settings', authenticateToken, (req, res) => {
  try {
    res.json({
      data: systemSettings
    });
  } catch (error) {
    console.error('获取系统设置失败:', error);
    res.status(500).json({ message: '获取系统设置失败' });
  }
});

// 更新系统设置
app.put('/api/settings', authenticateToken, requireTeacher, (req, res) => {
  try {
    const { certificate_submission_enabled, certificate_submission_deadline, certificate_submission_start, announcement } = req.body;

    if (certificate_submission_enabled !== undefined) {
      systemSettings.certificate_submission_enabled = certificate_submission_enabled;
    }
    if (certificate_submission_deadline !== undefined) {
      systemSettings.certificate_submission_deadline = certificate_submission_deadline;
    }
    if (certificate_submission_start !== undefined) {
      systemSettings.certificate_submission_start = certificate_submission_start;
    }
    if (announcement !== undefined) {
      systemSettings.announcement = announcement;
    }

    console.log('更新系统设置:', systemSettings);

    res.json({
      message: '系统设置更新成功',
      data: systemSettings
    });
  } catch (error) {
    console.error('更新系统设置失败:', error);
    res.status(500).json({ message: '更新系统设置失败' });
  }
});

// Excel相关API
app.post('/api/excel/import-students', authenticateToken, requireTeacher, upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: '请选择要导入的Excel文件' });
    }

    const ExcelJS = require('exceljs');
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(req.file.path);

    const worksheet = workbook.getWorksheet(1);
    if (!worksheet) {
      return res.status(400).json({ message: 'Excel文件格式错误' });
    }

    const results = {
      success_count: 0,
      error_count: 0,
      errors: []
    };

    // 跳过标题行，从第2行开始读取
    for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
      const row = worksheet.getRow(rowNumber);

      // 检查是否为空行
      if (!row.getCell(1).value && !row.getCell(2).value && !row.getCell(3).value) {
        continue;
      }

      const student_id = row.getCell(1).value?.toString().trim();
      const name = row.getCell(2).value?.toString().trim();
      const class_name = row.getCell(3).value?.toString().trim();
      const password = row.getCell(4).value?.toString().trim() || student_id;

      // 数据验证
      if (!student_id || !name || !class_name) {
        results.error_count++;
        results.errors.push(`第${rowNumber}行：学号、姓名、班级不能为空`);
        continue;
      }

      // 检查学号是否已存在
      const existingUser = users.find(u => u.student_id === student_id);
      if (existingUser) {
        results.error_count++;
        results.errors.push(`第${rowNumber}行：学号 ${student_id} 已存在`);
        continue;
      }

      // 添加学生
      const newStudent = {
        id: nextUserId++,
        student_id,
        name,
        class_name,
        password,
        role: 'student'
      };

      users.push(newStudent);
      results.success_count++;
      console.log('导入学生:', newStudent.name, newStudent.student_id);
    }

    if (results.success_count > 0) {
      saveData(); // 保存数据
    }

    // 删除临时文件
    fs.unlinkSync(req.file.path);

    res.json({
      message: `导入完成：成功 ${results.success_count} 个，失败 ${results.error_count} 个`,
      data: results
    });

  } catch (error) {
    console.error('导入失败:', error);
    res.status(500).json({ message: '导入失败：' + error.message });
  }
});

app.get('/api/excel/student-template', async (req, res) => {
  try {
    const ExcelJS = require('exceljs');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('学生信息模板');

    // 设置列宽
    worksheet.columns = [
      { header: '学号', key: 'student_id', width: 15 },
      { header: '姓名', key: 'name', width: 12 },
      { header: '班级', key: 'class_name', width: 25 },
      { header: '密码', key: 'password', width: 15 }
    ];

    // 设置标题行样式
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // 添加示例数据
    worksheet.addRow({
      student_id: '2024001001',
      name: '张三',
      class_name: '计算机科学与技术2024-1班',
      password: '2024001001'
    });

    worksheet.addRow({
      student_id: '2024001002',
      name: '李四',
      class_name: '计算机科学与技术2024-1班',
      password: '2024001002'
    });

    // 添加说明
    worksheet.addRow({});
    worksheet.addRow({
      student_id: '说明：',
      name: '1. 学号必须唯一',
      class_name: '2. 姓名和班级不能为空',
      password: '3. 密码为空时默认为学号'
    });

    // 设置响应头
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=student-import-template.xlsx');

    // 写入响应
    await workbook.xlsx.write(res);
    res.end();

  } catch (error) {
    console.error('下载模板失败:', error);
    res.status(500).json({ message: '下载模板失败：' + error.message });
  }
});

app.get('/api/excel/export-scores', authenticateToken, requirePermission(PERMISSIONS.GRADE_EXPORT), async (req, res) => {
  try {
    const { class_name, audit_activity_id } = req.query;

    // 获取审核活动信息
    let activityInfo = null;
    if (audit_activity_id) {
      activityInfo = auditActivities.find(a => a.id === parseInt(audit_activity_id));
      if (!activityInfo) {
        return res.status(404).json({ message: '审核活动不存在' });
      }
    }

    const ExcelJS = require('exceljs');
    const workbook = new ExcelJS.Workbook();

    // 根据审核活动设置工作表名称
    const sheetName = activityInfo ? `${activityInfo.name}-成绩表` : '学生成绩表';
    const worksheet = workbook.addWorksheet(sheetName);

    // 设置列（详细分类）
    worksheet.columns = [
      { header: '学号', key: 'student_id', width: 15 },
      { header: '姓名', key: 'name', width: 12 },
      { header: '班级', key: 'class_name', width: 25 },
      { header: '基础分值', key: 'base_score', width: 12 },

      // 专业类详细分类
      { header: '学科竞赛', key: 'academic_competition', width: 12 },
      { header: '实践技能', key: 'practical_skills', width: 12 },
      { header: '创新项目', key: 'innovation_project', width: 12 },
      { header: '专业认证', key: 'professional_cert', width: 12 },
      { header: '科学研究', key: 'research', width: 12 },
      { header: '专业类小计', key: 'professional_total', width: 15 },

      // 体育美育类详细分类
      { header: '体育竞赛', key: 'sports_competition', width: 12 },
      { header: '文艺活动', key: 'arts_activity', width: 12 },
      { header: '英语六级', key: 'english_cet6', width: 12 },
      { header: '体育美育小计', key: 'sports_arts_total', width: 15 },

      // 文明品德类详细分类
      { header: '学生干部', key: 'student_leader', width: 12 },
      { header: '志愿服务', key: 'volunteer_service', width: 12 },
      { header: '新闻采编', key: 'news_writing', width: 12 },
      { header: '道德表彰', key: 'moral_honor', width: 12 },
      { header: '文明品德小计', key: 'moral_total', width: 15 },

      { header: '总分', key: 'total', width: 10 },
      { header: '最终成绩', key: 'final', width: 12 },
      { header: '证书数量', key: 'certificate_count', width: 12 },
      { header: '更新时间', key: 'updated_at', width: 20 }
    ];

    // 设置标题行样式
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // 获取学生成绩数据
    let studentsWithScores = users.filter(u => u.role === 'student').map(user => {
      // 根据审核活动筛选证书
      let userCertificates;
      if (audit_activity_id) {
        userCertificates = certificates.filter(c =>
          c.user_id === user.id &&
          c.status === 'approved' &&
          c.audit_activity_id === parseInt(audit_activity_id)
        );
      } else {
        userCertificates = certificates.filter(c => c.user_id === user.id && c.status === 'approved');
      }

      // 优先使用手动修改的成绩，否则使用计算的成绩
      let scores;
      if (user.manual_scores && !audit_activity_id) {
        // 只有在查看全部成绩时才使用手动修改的成绩
        scores = user.manual_scores;
      } else {
        scores = calculateScore(userCertificates);
      }

      return {
        student_id: user.student_id,
        name: user.name,
        class_name: user.class_name,

        // 基础分值
        base_score: scores.base_score,

        // 专业类详细分类
        academic_competition: scores.academic_competition,
        practical_skills: scores.practical_skills,
        innovation_project: scores.innovation_project,
        professional_cert: scores.professional_cert,
        research: scores.research,
        professional_total: scores.professional_total,

        // 体育美育类详细分类
        sports_competition: scores.sports_competition,
        arts_activity: scores.arts_activity,
        english_cet6: scores.english_cet6,
        sports_arts_total: scores.sports_arts_total,

        // 文明品德类详细分类
        student_leader: scores.student_leader,
        volunteer_service: scores.volunteer_service,
        news_writing: scores.news_writing,
        moral_honor: scores.moral_honor,
        moral_total: scores.moral_total,

        // 总分和最终成绩
        total: scores.total,
        final: scores.final.toFixed ? scores.final.toFixed(2) : scores.final,

        certificate_count: userCertificates.length,
        updated_at: user.updated_at || new Date().toLocaleString('zh-CN')
      };
    });

    // 班级过滤
    if (class_name) {
      studentsWithScores = studentsWithScores.filter(s => s.class_name === class_name);
    }

    // 按最终成绩排序
    studentsWithScores.sort((a, b) => parseFloat(b.final_score) - parseFloat(a.final_score));

    // 添加数据
    studentsWithScores.forEach(student => {
      worksheet.addRow(student);
    });

    // 添加统计信息
    worksheet.addRow({});
    worksheet.addRow({
      student_id: '统计信息：',
      name: `总人数：${studentsWithScores.length}`,
      class_name: `导出时间：${new Date().toLocaleString('zh-CN')}`,
      base_score: '',
      professional_score: '',
      sports_arts_score: '',
      moral_score: '',
      total_score: '',
      final_score: '',
      certificate_count: '',
      updated_at: ''
    });

    // 设置响应头
    let filename;
    if (activityInfo) {
      filename = class_name ?
        `${activityInfo.name}-${class_name}-成绩表-${new Date().toISOString().split('T')[0]}.xlsx` :
        `${activityInfo.name}-成绩表-${new Date().toISOString().split('T')[0]}.xlsx`;
    } else {
      filename = class_name ?
        `${class_name}-成绩表-${new Date().toISOString().split('T')[0]}.xlsx` :
        `全部学生成绩表-${new Date().toISOString().split('T')[0]}.xlsx`;
    }

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename=${encodeURIComponent(filename)}`);

    // 记录操作日志
    logOperation(req.user.id, '导出成绩', {
      activity_name: activityInfo?.name || '全部活动',
      activity_id: audit_activity_id || null,
      class_name: class_name || '全部班级',
      student_count: studentsWithScores.length,
      filename
    }, 'export', null, req);

    // 写入响应
    await workbook.xlsx.write(res);
    res.end();

  } catch (error) {
    console.error('导出成绩失败:', error);
    res.status(500).json({ message: '导出成绩失败：' + error.message });
  }
});

// 前端路由支持 - 所有非API请求都返回index.html
app.get('*', (req, res) => {
  // 如果是API请求，返回404
  if (req.path.startsWith('/api/')) {
    return res.status(404).json({ message: 'API endpoint not found' });
  }

  // 返回前端应用
  res.sendFile(path.join(__dirname, '../client/dist/index.html'));
});

// 启动服务器
app.listen(PORT, '0.0.0.0', () => {
  console.log('🚀 学生素质测评系统服务器运行在端口', PORT);
  console.log('🌐 服务器地址: http://0.0.0.0:' + PORT);
  console.log('📋 管理员账户:');
  console.log('👨‍🏫 管理员: admin / admin123');

  // 显示数据加载状态
  const studentCount = users.filter(u => u.role === 'student').length;
  const certificateCount = certificates.length;
  console.log('');
  console.log('📊 数据状态:');
  console.log(`   👥 学生数量: ${studentCount}`);
  console.log(`   📜 证书数量: ${certificateCount}`);
  console.log(`   💾 数据文件: ${fs.existsSync(DATA_FILE) ? '存在' : '不存在'}`);

  console.log('');
  console.log('📚 证书类别配置:');
  categories.forEach((cat, index) => {
    console.log(`   ${index + 1}. ${cat.category_name} (${cat.type}类, 最高${cat.max_score}分)`);
  });
  console.log('');
  console.log('✅ Excel导入导出功能已启用');
  console.log('✅ 批量学生管理功能已启用');
  console.log('✅ 自动成绩计算功能已启用');
  console.log('✅ 数据持久化功能已启用');
});
