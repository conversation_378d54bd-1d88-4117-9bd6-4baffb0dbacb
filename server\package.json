{"name": "quality-assessment-server", "version": "1.0.0", "description": "素质测评系统后端服务", "main": "sqlite-server.js", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "scripts": {"start": "node sqlite-server.js", "dev": "nodemon sqlite-server.js", "init-db": "node scripts/initDatabase.js", "build": "echo 'No build step required'", "postinstall": "echo 'Dependencies installed successfully'"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "xlsx": "^0.18.5"}, "devDependencies": {"nodemon": "^3.0.2"}}