import { supabase } from './supabase';
import toast from 'react-hot-toast';

class AuthService {
  constructor() {
    this.currentUser = null;
    this.session = null;
  }

  // Initialize auth service
  async initialize() {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (session) {
        this.session = session;
        await this.fetchUserProfile(session.user.id);
      }
      
      return this.currentUser;
    } catch (error) {
      console.error('Auth initialization error:', error);
      return null;
    }
  }

  // Fetch user profile from database
  async fetchUserProfile(userId) {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) throw error;

      this.currentUser = data;
      return data;
    } catch (error) {
      console.error('Error fetching user profile:', error);
      throw error;
    }
  }

  // Login with student_id and password
  async login(studentId, password) {
    try {
      // First, find the user by student_id
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('student_id', studentId)
        .single();

      if (userError || !userData) {
        throw new Error('用户不存在');
      }

      // For production, implement proper password verification
      // This is a simplified version for demonstration
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          student_id: studentId,
          password: password,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || '登录失败');
      }

      // Store session info
      if (result.session) {
        // Set session in Supabase client
        await supabase.auth.setSession({
          access_token: result.session.access_token,
          refresh_token: result.session.refresh_token,
        });
      }

      this.currentUser = result.user;
      this.session = result.session;

      return result.user;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  // Logout
  async logout() {
    try {
      await supabase.auth.signOut();
      this.currentUser = null;
      this.session = null;
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  }

  // Change password
  async changePassword(oldPassword, newPassword) {
    try {
      if (!this.currentUser) {
        throw new Error('用户未登录');
      }

      const response = await fetch('/api/auth/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.session?.access_token}`,
        },
        body: JSON.stringify({
          old_password: oldPassword,
          new_password: newPassword,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || '密码修改失败');
      }

      return result;
    } catch (error) {
      console.error('Change password error:', error);
      throw error;
    }
  }

  // Check if user has specific permission
  hasPermission(permission) {
    if (!this.currentUser) return false;
    if (this.currentUser.is_super_admin) return true;
    return this.currentUser.permissions?.includes(permission) || false;
  }

  // Check if user has specific role
  hasRole(role) {
    return this.currentUser?.role === role;
  }

  // Get current user
  getCurrentUser() {
    return this.currentUser;
  }

  // Get current session
  getSession() {
    return this.session;
  }

  // Check if user is authenticated
  isAuthenticated() {
    return !!this.currentUser && !!this.session;
  }

  // Get auth token for API requests
  getAuthToken() {
    return this.session?.access_token;
  }

  // Listen for auth state changes
  onAuthStateChange(callback) {
    return supabase.auth.onAuthStateChange(async (event, session) => {
      this.session = session;
      
      if (session) {
        try {
          await this.fetchUserProfile(session.user.id);
        } catch (error) {
          console.error('Error fetching user profile on auth change:', error);
          this.currentUser = null;
        }
      } else {
        this.currentUser = null;
      }
      
      callback(event, session, this.currentUser);
    });
  }

  // Refresh session
  async refreshSession() {
    try {
      const { data: { session }, error } = await supabase.auth.refreshSession();
      
      if (error) throw error;
      
      this.session = session;
      
      if (session) {
        await this.fetchUserProfile(session.user.id);
      }
      
      return session;
    } catch (error) {
      console.error('Session refresh error:', error);
      throw error;
    }
  }

  // Create user account (for admin use)
  async createUser(userData) {
    try {
      if (!this.hasPermission('student_management')) {
        throw new Error('权限不足');
      }

      const response = await fetch('/api/users/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`,
        },
        body: JSON.stringify(userData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || '创建用户失败');
      }

      return result;
    } catch (error) {
      console.error('Create user error:', error);
      throw error;
    }
  }

  // Reset user password (for admin use)
  async resetUserPassword(userId, newPassword) {
    try {
      if (!this.hasPermission('student_management')) {
        throw new Error('权限不足');
      }

      const response = await fetch(`/api/users/${userId}/reset-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`,
        },
        body: JSON.stringify({ new_password: newPassword }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || '重置密码失败');
      }

      return result;
    } catch (error) {
      console.error('Reset password error:', error);
      throw error;
    }
  }
}

// Create singleton instance
const authService = new AuthService();

export default authService;
