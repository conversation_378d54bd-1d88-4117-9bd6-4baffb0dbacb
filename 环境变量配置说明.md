# 环境变量配置说明

## Vercel部署环境变量配置

在Vercel部署时，需要在项目设置中添加以下环境变量：

### Vercel环境变量配置 (必须添加以下所有变量)
```
# 前端环境变量
VITE_SUPABASE_URL=https://appthjywqqzhqqxgktkr.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFwcHRoanl3cXF6aHFxeGdrdGtyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE4OTE1MTksImV4cCI6MjA2NzQ2NzUxOX0.JESFGQJCjRFQEQH73teYa7iGVdbZyrnMh-acxWV3Uiw

SUPABASE_URL=https://appthjywqqzhqqxgktkr.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFwcHRoanl3cXF6aHFxeGdrdGtyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTg5MTUxOSwiZXhwIjoyMDY3NDY3NTE5fQ.bchBxRPo6lh4fHllvC_8Mbwe6Cfgbl45Ym-MutRvlHI
SUPABASE_JWT_SECRET=f97dKg5usn3YpHq1E1UR1iBfNqHzSilBety0cK8uAgX63NXZkbvcpX3o2fFxOUiSiol7dwoBq1if7mtuYcTe4w==
NODE_ENV=production
```

## 配置步骤

### 1. 在Vercel Dashboard中配置
1. 登录 [Vercel Dashboard](https://vercel.com/dashboard)
2. 选择您的项目
3. 进入 **Settings** → **Environment Variables**
4. 逐一添加上述环境变量

### 2. 环境变量说明

| 变量名 | 用途 | 值 |
|--------|------|-----|
| `VITE_SUPABASE_URL` | 前端Supabase项目URL | https://appthjywqqzhqqxgktkr.supabase.co |
| `VITE_SUPABASE_ANON_KEY` | 前端匿名访问密钥 | eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... |
| `SUPABASE_URL` | 后端Supabase项目URL | https://appthjywqqzhqqxgktkr.supabase.co |
| `SUPABASE_SERVICE_ROLE_KEY` | 后端服务角色密钥 | eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... |
| `SUPABASE_JWT_SECRET` | JWT签名密钥 | f97dKg5usn3YpHq1E1UR1iBfNqHzSilBety0cK8uAgX63NXZkbvcpX3o2fFxOUiSiol7dwoBq1if7mtuYcTe4w== |
| `NODE_ENV` | 运行环境 | production |

### 🚨 重要提醒：必须添加所有6个环境变量！

**在Vercel Dashboard中添加环境变量的步骤：**
1. 登录 Vercel Dashboard
2. 选择项目 → Settings → Environment Variables
3. 逐一添加上述6个变量
4. 每个变量都选择 "All Environments"
5. 保存后重新部署项目

### 3. 安全注意事项

⚠️ **重要安全提醒**：
- `VITE_` 前缀的变量会暴露给前端，只能包含公开信息
- `SUPABASE_SERVICE_ROLE_KEY` 和 `SUPABASE_JWT_SECRET` 是敏感信息，只能在服务端使用
- 不要在前端代码中直接使用服务角色密钥
- 定期轮换密钥以确保安全

### 4. 验证配置

部署完成后，可以通过以下方式验证配置：
1. 检查前端是否能正常连接Supabase
2. 测试用户登录功能
3. 验证数据库读写操作
4. 确认文件上传功能正常

## 故障排除

### 常见问题
1. **连接失败**：检查URL和密钥是否正确复制
2. **权限错误**：确认使用了正确的密钥类型（anon vs service_role）
3. **CORS错误**：检查Supabase项目的CORS设置
4. **环境变量未生效**：重新部署项目以应用新的环境变量

### 联系支持
如遇到配置问题，请检查：
- Supabase项目状态
- Vercel部署日志
- 浏览器开发者工具的网络请求

---
**更新时间**：2025年7月7日
**配置版本**：v2.0
