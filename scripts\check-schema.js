import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function checkTableSchema() {
  try {
    console.log('🔍 检查数据库表结构...\n');
    
    // 检查用户表结构
    console.log('👤 用户表 (users) 结构:');
    try {
      const { data: userData, error } = await supabase
        .from('users')
        .select('*')
        .limit(1);

      if (!error && userData && userData.length > 0) {
        console.log('用户表列名:', Object.keys(userData[0]));
        console.log('示例数据:', userData[0]);
      } else {
        console.log('用户表查询失败或为空:', error);
      }
    } catch (err) {
      console.log('用户表检查出错:', err.message);
    }
    
    // 检查证书表结构
    console.log('\n📜 证书表 (certificates) 结构:');
    const { data: certData, error: certError } = await supabase
      .from('certificates')
      .select('*')
      .limit(1);
    
    if (!certError && certData && certData.length > 0) {
      console.log('证书表列名:', Object.keys(certData[0]));
    } else {
      console.log('证书表为空或查询失败');
    }
    
    // 检查证书类别表结构
    console.log('\n📋 证书类别表 (certificate_categories) 结构:');
    const { data: catData, error: catError } = await supabase
      .from('certificate_categories')
      .select('*')
      .limit(1);
    
    if (!catError && catData && catData.length > 0) {
      console.log('证书类别表列名:', Object.keys(catData[0]));
    }
    
    // 检查审核活动表结构
    console.log('\n🎯 审核活动表 (audit_activities) 结构:');
    const { data: actData, error: actError } = await supabase
      .from('audit_activities')
      .select('*')
      .limit(1);
    
    if (!actError && actData && actData.length > 0) {
      console.log('审核活动表列名:', Object.keys(actData[0]));
    }
    
  } catch (error) {
    console.error('❌ 检查表结构失败:', error);
  }
}

checkTableSchema();
