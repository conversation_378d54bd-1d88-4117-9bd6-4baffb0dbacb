@echo off
echo ========================================
echo    学生素质测评系统停止脚本
echo ========================================
echo.

echo 正在停止Node.js进程...
taskkill /f /im node.exe >nul 2>&1

echo 正在停止npm进程...
taskkill /f /im npm.cmd >nul 2>&1

echo 正在停止相关进程...
for /f "tokens=2" %%i in ('netstat -ano ^| findstr :5000') do taskkill /f /pid %%i >nul 2>&1
for /f "tokens=2" %%i in ('netstat -ano ^| findstr :3001') do taskkill /f /pid %%i >nul 2>&1

echo.
echo ========================================
echo 系统已停止！
echo ========================================
echo.
echo 按任意键退出...
pause >nul
