import{r as s,R as Ue}from"./vendor-11b42a5c.js";/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function F(){return F=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},F.apply(this,arguments)}var N;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(N||(N={}));const ue="popstate";function Oe(e){e===void 0&&(e={});function t(r,a){let{pathname:i,search:o,hash:u}=r.location;return Z("",{pathname:i,search:o,hash:u},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function n(r,a){return typeof a=="string"?a:k(a)}return Ie(t,n,null,e)}function R(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function E(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function _e(){return Math.random().toString(36).substr(2,8)}function ce(e,t){return{usr:e.state,key:e.key,idx:t}}function Z(e,t,n,r){return n===void 0&&(n=null),F({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?I(t):t,{state:n,key:t&&t.key||r||_e()})}function k(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function I(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function Ie(e,t,n,r){r===void 0&&(r={});let{window:a=document.defaultView,v5Compat:i=!1}=r,o=a.history,u=N.Pop,l=null,c=h();c==null&&(c=0,o.replaceState(F({},o.state,{idx:c}),""));function h(){return(o.state||{idx:null}).idx}function d(){u=N.Pop;let m=h(),x=m==null?null:m-c;c=m,l&&l({action:u,location:v.location,delta:x})}function p(m,x){u=N.Push;let f=Z(v.location,m,x);n&&n(f,m),c=h()+1;let g=ce(f,c),b=v.createHref(f);try{o.pushState(g,"",b)}catch(L){if(L instanceof DOMException&&L.name==="DataCloneError")throw L;a.location.assign(b)}i&&l&&l({action:u,location:v.location,delta:1})}function w(m,x){u=N.Replace;let f=Z(v.location,m,x);n&&n(f,m),c=h();let g=ce(f,c),b=v.createHref(f);o.replaceState(g,"",b),i&&l&&l({action:u,location:v.location,delta:0})}function y(m){let x=a.location.origin!=="null"?a.location.origin:a.location.href,f=typeof m=="string"?m:k(m);return f=f.replace(/ $/,"%20"),R(x,"No window.location.(origin|href) available to create URL for href: "+f),new URL(f,x)}let v={get action(){return u},get location(){return e(a,o)},listen(m){if(l)throw new Error("A history only accepts one active listener");return a.addEventListener(ue,d),l=m,()=>{a.removeEventListener(ue,d),l=null}},createHref(m){return t(a,m)},createURL:y,encodeLocation(m){let x=y(m);return{pathname:x.pathname,search:x.search,hash:x.hash}},push:p,replace:w,go(m){return o.go(m)}};return v}var de;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(de||(de={}));function Be(e,t,n){return n===void 0&&(n="/"),Fe(e,t,n,!1)}function Fe(e,t,n,r){let a=typeof t=="string"?I(t):t,i=T(a.pathname||"/",n);if(i==null)return null;let o=ge(e);ke(o);let u=null;for(let l=0;u==null&&l<o.length;++l){let c=He(i);u=Je(o[l],c,r)}return u}function ge(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let a=(i,o,u)=>{let l={relativePath:u===void 0?i.path||"":u,caseSensitive:i.caseSensitive===!0,childrenIndex:o,route:i};l.relativePath.startsWith("/")&&(R(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let c=P([r,l.relativePath]),h=n.concat(l);i.children&&i.children.length>0&&(R(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+c+'".')),ge(i.children,t,h,c)),!(i.path==null&&!i.index)&&t.push({path:c,score:Ve(c,i.index),routesMeta:h})};return e.forEach((i,o)=>{var u;if(i.path===""||!((u=i.path)!=null&&u.includes("?")))a(i,o);else for(let l of ye(i.path))a(i,o,l)}),t}function ye(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,a=n.endsWith("?"),i=n.replace(/\?$/,"");if(r.length===0)return a?[i,""]:[i];let o=ye(r.join("/")),u=[];return u.push(...o.map(l=>l===""?i:[i,l].join("/"))),a&&u.push(...o),u.map(l=>e.startsWith("/")&&l===""?"/":l)}function ke(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:ze(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const De=/^:[\w-]+$/,Ae=3,We=2,Me=1,$e=10,je=-2,he=e=>e==="*";function Ve(e,t){let n=e.split("/"),r=n.length;return n.some(he)&&(r+=je),t&&(r+=We),n.filter(a=>!he(a)).reduce((a,i)=>a+(De.test(i)?Ae:i===""?Me:$e),r)}function ze(e,t){return e.length===t.length&&e.slice(0,-1).every((r,a)=>r===t[a])?e[e.length-1]-t[t.length-1]:0}function Je(e,t,n){n===void 0&&(n=!1);let{routesMeta:r}=e,a={},i="/",o=[];for(let u=0;u<r.length;++u){let l=r[u],c=u===r.length-1,h=i==="/"?t:t.slice(i.length)||"/",d=J({path:l.relativePath,caseSensitive:l.caseSensitive,end:c},h),p=l.route;if(!d&&c&&n&&!r[r.length-1].route.index&&(d=J({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},h)),!d)return null;Object.assign(a,d.params),o.push({params:a,pathname:P([i,d.pathname]),pathnameBase:Xe(P([i,d.pathnameBase])),route:p}),d.pathnameBase!=="/"&&(i=P([i,d.pathnameBase]))}return o}function J(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=Ke(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let i=a[0],o=i.replace(/(.)\/+$/,"$1"),u=a.slice(1);return{params:r.reduce((c,h,d)=>{let{paramName:p,isOptional:w}=h;if(p==="*"){let v=u[d]||"";o=i.slice(0,i.length-v.length).replace(/(.)\/+$/,"$1")}const y=u[d];return w&&!y?c[p]=void 0:c[p]=(y||"").replace(/%2F/g,"/"),c},{}),pathname:i,pathnameBase:o,pattern:e}}function Ke(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),E(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,u,l)=>(r.push({paramName:u,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}function He(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return E(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function T(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function Ye(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:a=""}=typeof e=="string"?I(e):e;return{pathname:n?n.startsWith("/")?n:qe(n,t):t,search:Qe(r),hash:Ze(a)}}function qe(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?n.length>1&&n.pop():a!=="."&&n.push(a)}),n.length>1?n.join("/"):"/"}function X(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Ge(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function te(e,t){let n=Ge(e);return t?n.map((r,a)=>a===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function ne(e,t,n,r){r===void 0&&(r=!1);let a;typeof e=="string"?a=I(e):(a=F({},e),R(!a.pathname||!a.pathname.includes("?"),X("?","pathname","search",a)),R(!a.pathname||!a.pathname.includes("#"),X("#","pathname","hash",a)),R(!a.search||!a.search.includes("#"),X("#","search","hash",a)));let i=e===""||a.pathname==="",o=i?"/":a.pathname,u;if(o==null)u=n;else{let d=t.length-1;if(!r&&o.startsWith("..")){let p=o.split("/");for(;p[0]==="..";)p.shift(),d-=1;a.pathname=p.join("/")}u=d>=0?t[d]:"/"}let l=Ye(a,u),c=o&&o!=="/"&&o.endsWith("/"),h=(i||o===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(c||h)&&(l.pathname+="/"),l}const P=e=>e.join("/").replace(/\/\/+/g,"/"),Xe=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Qe=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Ze=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function et(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const xe=["post","put","patch","delete"];new Set(xe);const tt=["get",...xe];new Set(tt);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function D(){return D=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},D.apply(this,arguments)}const W=s.createContext(null);W.displayName="DataRouter";const re=s.createContext(null);re.displayName="DataRouterState";const nt=s.createContext(null);nt.displayName="Await";const C=s.createContext(null);C.displayName="Navigation";const M=s.createContext(null);M.displayName="Location";const S=s.createContext({outlet:null,matches:[],isDataRoute:!1});S.displayName="Route";const ae=s.createContext(null);ae.displayName="RouteError";function rt(e,t){let{relative:n}=t===void 0?{}:t;B()||R(!1,"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:a}=s.useContext(C),{hash:i,pathname:o,search:u}=$(e,{relative:n}),l=o;return r!=="/"&&(l=o==="/"?r:P([r,o])),a.createHref({pathname:l,search:u,hash:i})}function B(){return s.useContext(M)!=null}function U(){return B()||R(!1,"useLocation() may be used only in the context of a <Router> component."),s.useContext(M).location}const Re="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function be(e){s.useContext(C).static||s.useLayoutEffect(e)}function we(){let{isDataRoute:e}=s.useContext(S);return e?xt():at()}function at(){B()||R(!1,"useNavigate() may be used only in the context of a <Router> component.");let e=s.useContext(W),{basename:t,future:n,navigator:r}=s.useContext(C),{matches:a}=s.useContext(S),{pathname:i}=U(),o=JSON.stringify(te(a,n.v7_relativeSplatPath)),u=s.useRef(!1);return be(()=>{u.current=!0}),s.useCallback(function(c,h){if(h===void 0&&(h={}),E(u.current,Re),!u.current)return;if(typeof c=="number"){r.go(c);return}let d=ne(c,JSON.parse(o),i,h.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:P([t,d.pathname])),(h.replace?r.replace:r.push)(d,h.state,h)},[t,r,o,i,e])}const it=s.createContext(null);function ot(e){let t=s.useContext(S).outlet;return t&&s.createElement(it.Provider,{value:e},t)}function $(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=s.useContext(C),{matches:a}=s.useContext(S),{pathname:i}=U(),o=JSON.stringify(te(a,r.v7_relativeSplatPath));return s.useMemo(()=>ne(e,JSON.parse(o),i,n==="path"),[e,o,i,n])}function lt(e,t){return st(e,t)}function st(e,t,n,r){B()||R(!1,"useRoutes() may be used only in the context of a <Router> component.");let{navigator:a}=s.useContext(C),{matches:i}=s.useContext(S),o=i[i.length-1],u=o?o.params:{},l=o?o.pathname:"/",c=o?o.pathnameBase:"/",h=o&&o.route;{let f=h&&h.path||"";Ee(l,!h||f.endsWith("*"),"You rendered descendant <Routes> (or called `useRoutes()`) at "+('"'+l+'" (under <Route path="'+f+'">) but the ')+`parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

`+('Please change the parent <Route path="'+f+'"> to <Route ')+('path="'+(f==="/"?"*":f+"/*")+'">.'))}let d=U(),p;if(t){var w;let f=typeof t=="string"?I(t):t;c==="/"||(w=f.pathname)!=null&&w.startsWith(c)||R(!1,"When overriding the location using `<Routes location>` or `useRoutes(routes, location)`, the location pathname must begin with the portion of the URL pathname that was "+('matched by all parent routes. The current pathname base is "'+c+'" ')+('but pathname "'+f.pathname+'" was given in the `location` prop.')),p=f}else p=d;let y=p.pathname||"/",v=y;if(c!=="/"){let f=c.replace(/^\//,"").split("/");v="/"+y.replace(/^\//,"").split("/").slice(f.length).join("/")}let m=Be(e,{pathname:v});E(h||m!=null,'No routes matched location "'+p.pathname+p.search+p.hash+'" '),E(m==null||m[m.length-1].route.element!==void 0||m[m.length-1].route.Component!==void 0||m[m.length-1].route.lazy!==void 0,'Matched leaf route at location "'+p.pathname+p.search+p.hash+'" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.');let x=ft(m&&m.map(f=>Object.assign({},f,{params:Object.assign({},u,f.params),pathname:P([c,a.encodeLocation?a.encodeLocation(f.pathname).pathname:f.pathname]),pathnameBase:f.pathnameBase==="/"?c:P([c,a.encodeLocation?a.encodeLocation(f.pathnameBase).pathname:f.pathnameBase])})),i,n,r);return t&&x?s.createElement(M.Provider,{value:{location:D({pathname:"/",search:"",hash:"",state:null,key:"default"},p),navigationType:N.Pop}},x):x}function ut(){let e=yt(),t=et(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",a={padding:"0.5rem",backgroundColor:r},i={padding:"2px 4px",backgroundColor:r},o=null;return console.error("Error handled by React Router default ErrorBoundary:",e),o=s.createElement(s.Fragment,null,s.createElement("p",null,"💿 Hey developer 👋"),s.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",s.createElement("code",{style:i},"ErrorBoundary")," or"," ",s.createElement("code",{style:i},"errorElement")," prop on your route.")),s.createElement(s.Fragment,null,s.createElement("h2",null,"Unexpected Application Error!"),s.createElement("h3",{style:{fontStyle:"italic"}},t),n?s.createElement("pre",{style:a},n):null,o)}const ct=s.createElement(ut,null);class dt extends s.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?s.createElement(S.Provider,{value:this.props.routeContext},s.createElement(ae.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function ht(e){let{routeContext:t,match:n,children:r}=e,a=s.useContext(W);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),s.createElement(S.Provider,{value:t},r)}function ft(e,t,n,r){var a;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var i;if(!n)return null;if(n.errors)e=n.matches;else if((i=r)!=null&&i.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let o=e,u=(a=n)==null?void 0:a.errors;if(u!=null){let h=o.findIndex(d=>d.route.id&&(u==null?void 0:u[d.route.id])!==void 0);h>=0||R(!1,"Could not find a matching route for errors on route IDs: "+Object.keys(u).join(",")),o=o.slice(0,Math.min(o.length,h+1))}let l=!1,c=-1;if(n&&r&&r.v7_partialHydration)for(let h=0;h<o.length;h++){let d=o[h];if((d.route.HydrateFallback||d.route.hydrateFallbackElement)&&(c=h),d.route.id){let{loaderData:p,errors:w}=n,y=d.route.loader&&p[d.route.id]===void 0&&(!w||w[d.route.id]===void 0);if(d.route.lazy||y){l=!0,c>=0?o=o.slice(0,c+1):o=[o[0]];break}}}return o.reduceRight((h,d,p)=>{let w,y=!1,v=null,m=null;n&&(w=u&&d.route.id?u[d.route.id]:void 0,v=d.route.errorElement||ct,l&&(c<0&&p===0?(Ee("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),y=!0,m=null):c===p&&(y=!0,m=d.route.hydrateFallbackElement||null)));let x=t.concat(o.slice(0,p+1)),f=()=>{let g;return w?g=v:y?g=m:d.route.Component?g=s.createElement(d.route.Component,null):d.route.element?g=d.route.element:g=h,s.createElement(ht,{match:d,routeContext:{outlet:h,matches:x,isDataRoute:n!=null},children:g})};return n&&(d.route.ErrorBoundary||d.route.errorElement||p===0)?s.createElement(dt,{location:n.location,revalidation:n.revalidation,component:v,error:w,children:f(),routeContext:{outlet:null,matches:x,isDataRoute:!0}}):f()},null)}var Ce=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Ce||{}),A=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(A||{});function ie(e){return e+" must be used within a data router.  See https://reactrouter.com/v6/routers/picking-a-router."}function pt(e){let t=s.useContext(W);return t||R(!1,ie(e)),t}function mt(e){let t=s.useContext(re);return t||R(!1,ie(e)),t}function vt(e){let t=s.useContext(S);return t||R(!1,ie(e)),t}function oe(e){let t=vt(e),n=t.matches[t.matches.length-1];return n.route.id||R(!1,e+' can only be used on routes that contain a unique "id"'),n.route.id}function gt(){return oe(A.UseRouteId)}function yt(){var e;let t=s.useContext(ae),n=mt(A.UseRouteError),r=oe(A.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function xt(){let{router:e}=pt(Ce.UseNavigateStable),t=oe(A.UseNavigateStable),n=s.useRef(!1);return be(()=>{n.current=!0}),s.useCallback(function(a,i){i===void 0&&(i={}),E(n.current,Re),n.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,D({fromRouteId:t},i)))},[e,t])}const fe={};function Ee(e,t,n){!t&&!fe[e]&&(fe[e]=!0,E(!1,n))}const pe={};function Rt(e,t){pe[t]||(pe[t]=!0,console.warn(t))}const O=(e,t,n)=>Rt(e,"⚠️ React Router Future Flag Warning: "+t+". "+("You can use the `"+e+"` future flag to opt-in early. ")+("For more information, see "+n+"."));function bt(e,t){(e==null?void 0:e.v7_startTransition)===void 0&&O("v7_startTransition","React Router will begin wrapping state updates in `React.startTransition` in v7","https://reactrouter.com/v6/upgrading/future#v7_starttransition"),(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath===void 0)&&O("v7_relativeSplatPath","Relative route resolution within Splat routes is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath"),t&&(t.v7_fetcherPersist===void 0&&O("v7_fetcherPersist","The persistence behavior of fetchers is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_fetcherpersist"),t.v7_normalizeFormMethod===void 0&&O("v7_normalizeFormMethod","Casing of `formMethod` fields is being normalized to uppercase in v7","https://reactrouter.com/v6/upgrading/future#v7_normalizeformmethod"),t.v7_partialHydration===void 0&&O("v7_partialHydration","`RouterProvider` hydration behavior is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_partialhydration"),t.v7_skipActionErrorRevalidation===void 0&&O("v7_skipActionErrorRevalidation","The revalidation behavior after 4xx/5xx `action` responses is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_skipactionerrorrevalidation"))}function Xt(e){let{to:t,replace:n,state:r,relative:a}=e;B()||R(!1,"<Navigate> may be used only in the context of a <Router> component.");let{future:i,static:o}=s.useContext(C);E(!o,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:u}=s.useContext(S),{pathname:l}=U(),c=we(),h=ne(t,te(u,i.v7_relativeSplatPath),l,a==="path"),d=JSON.stringify(h);return s.useEffect(()=>c(JSON.parse(d),{replace:n,state:r,relative:a}),[c,d,a,n,r]),null}function Qt(e){return ot(e.context)}function wt(e){R(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function Ct(e){let{basename:t="/",children:n=null,location:r,navigationType:a=N.Pop,navigator:i,static:o=!1,future:u}=e;B()&&R(!1,"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let l=t.replace(/^\/*/,"/"),c=s.useMemo(()=>({basename:l,navigator:i,static:o,future:D({v7_relativeSplatPath:!1},u)}),[l,u,i,o]);typeof r=="string"&&(r=I(r));let{pathname:h="/",search:d="",hash:p="",state:w=null,key:y="default"}=r,v=s.useMemo(()=>{let m=T(h,l);return m==null?null:{location:{pathname:m,search:d,hash:p,state:w,key:y},navigationType:a}},[l,h,d,p,w,y,a]);return E(v!=null,'<Router basename="'+l+'"> is not able to match the URL '+('"'+h+d+p+'" because it does not start with the ')+"basename, so the <Router> won't render anything."),v==null?null:s.createElement(C.Provider,{value:c},s.createElement(M.Provider,{children:n,value:v}))}function Zt(e){let{children:t,location:n}=e;return lt(ee(t),n)}new Promise(()=>{});function ee(e,t){t===void 0&&(t=[]);let n=[];return s.Children.forEach(e,(r,a)=>{if(!s.isValidElement(r))return;let i=[...t,a];if(r.type===s.Fragment){n.push.apply(n,ee(r.props.children,i));return}r.type!==wt&&R(!1,"["+(typeof r.type=="string"?r.type:r.type.name)+"] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>"),!r.props.index||!r.props.children||R(!1,"An index route cannot have child routes.");let o={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(o.children=ee(r.props.children,i)),n.push(o)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function _(){return _=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},_.apply(this,arguments)}function le(e,t){if(e==null)return{};var n={},r=Object.keys(e),a,i;for(i=0;i<r.length;i++)a=r[i],!(t.indexOf(a)>=0)&&(n[a]=e[a]);return n}const V="get",z="application/x-www-form-urlencoded";function H(e){return e!=null&&typeof e.tagName=="string"}function Et(e){return H(e)&&e.tagName.toLowerCase()==="button"}function St(e){return H(e)&&e.tagName.toLowerCase()==="form"}function Pt(e){return H(e)&&e.tagName.toLowerCase()==="input"}function Lt(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Nt(e,t){return e.button===0&&(!t||t==="_self")&&!Lt(e)}let j=null;function Tt(){if(j===null)try{new FormData(document.createElement("form"),0),j=!1}catch{j=!0}return j}const Ut=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Q(e){return e!=null&&!Ut.has(e)?(E(!1,'"'+e+'" is not a valid `encType` for `<Form>`/`<fetcher.Form>` '+('and will default to "'+z+'"')),null):e}function Ot(e,t){let n,r,a,i,o;if(St(e)){let u=e.getAttribute("action");r=u?T(u,t):null,n=e.getAttribute("method")||V,a=Q(e.getAttribute("enctype"))||z,i=new FormData(e)}else if(Et(e)||Pt(e)&&(e.type==="submit"||e.type==="image")){let u=e.form;if(u==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let l=e.getAttribute("formaction")||u.getAttribute("action");if(r=l?T(l,t):null,n=e.getAttribute("formmethod")||u.getAttribute("method")||V,a=Q(e.getAttribute("formenctype"))||Q(u.getAttribute("enctype"))||z,i=new FormData(u,e),!Tt()){let{name:c,type:h,value:d}=e;if(h==="image"){let p=c?c+".":"";i.append(p+"x","0"),i.append(p+"y","0")}else c&&i.append(c,d)}}else{if(H(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=V,r=null,a=z,o=e}return i&&a==="text/plain"&&(o=i,i=void 0),{action:r,method:n.toLowerCase(),encType:a,formData:i,body:o}}const _t=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],It=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],Bt=["fetcherKey","navigate","reloadDocument","replace","state","method","action","onSubmit","relative","preventScrollReset","viewTransition"],Ft="6";try{window.__reactRouterVersion=Ft}catch{}const Se=s.createContext({isTransitioning:!1});Se.displayName="ViewTransition";const kt=s.createContext(new Map);kt.displayName="Fetchers";const Dt="startTransition",me=Ue[Dt];function en(e){let{basename:t,children:n,future:r,window:a}=e,i=s.useRef();i.current==null&&(i.current=Oe({window:a,v5Compat:!0}));let o=i.current,[u,l]=s.useState({action:o.action,location:o.location}),{v7_startTransition:c}=r||{},h=s.useCallback(d=>{c&&me?me(()=>l(d)):l(d)},[l,c]);return s.useLayoutEffect(()=>o.listen(h),[o,h]),s.useEffect(()=>bt(r),[r]),s.createElement(Ct,{basename:t,children:n,location:u.location,navigationType:u.action,navigator:o,future:r})}const At=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Wt=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Pe=s.forwardRef(function(t,n){let{onClick:r,relative:a,reloadDocument:i,replace:o,state:u,target:l,to:c,preventScrollReset:h,viewTransition:d}=t,p=le(t,_t),{basename:w}=s.useContext(C),y,v=!1;if(typeof c=="string"&&Wt.test(c)&&(y=c,At))try{let g=new URL(window.location.href),b=c.startsWith("//")?new URL(g.protocol+c):new URL(c),L=T(b.pathname,w);b.origin===g.origin&&L!=null?c=L+b.search+b.hash:v=!0}catch{E(!1,'<Link to="'+c+'"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.')}let m=rt(c,{relative:a}),x=Vt(c,{replace:o,state:u,target:l,preventScrollReset:h,relative:a,viewTransition:d});function f(g){r&&r(g),g.defaultPrevented||x(g)}return s.createElement("a",_({},p,{href:y||m,onClick:v||i?r:f,ref:n,target:l}))});Pe.displayName="Link";const Mt=s.forwardRef(function(t,n){let{"aria-current":r="page",caseSensitive:a=!1,className:i="",end:o=!1,style:u,to:l,viewTransition:c,children:h}=t,d=le(t,It),p=$(l,{relative:d.relative}),w=U(),y=s.useContext(re),{navigator:v,basename:m}=s.useContext(C),x=y!=null&&qt(p)&&c===!0,f=v.encodeLocation?v.encodeLocation(p).pathname:p.pathname,g=w.pathname,b=y&&y.navigation&&y.navigation.location?y.navigation.location.pathname:null;a||(g=g.toLowerCase(),b=b?b.toLowerCase():null,f=f.toLowerCase()),b&&m&&(b=T(b,m)||b);const L=f!=="/"&&f.endsWith("/")?f.length-1:f.length;let Y=g===f||!o&&g.startsWith(f)&&g.charAt(L)==="/",se=b!=null&&(b===f||!o&&b.startsWith(f)&&b.charAt(f.length)==="/"),q={isActive:Y,isPending:se,isTransitioning:x},Ne=Y?r:void 0,G;typeof i=="function"?G=i(q):G=[i,Y?"active":null,se?"pending":null,x?"transitioning":null].filter(Boolean).join(" ");let Te=typeof u=="function"?u(q):u;return s.createElement(Pe,_({},d,{"aria-current":Ne,className:G,ref:n,style:Te,to:l,viewTransition:c}),typeof h=="function"?h(q):h)});Mt.displayName="NavLink";const $t=s.forwardRef((e,t)=>{let{fetcherKey:n,navigate:r,reloadDocument:a,replace:i,state:o,method:u=V,action:l,onSubmit:c,relative:h,preventScrollReset:d,viewTransition:p}=e,w=le(e,Bt),y=Ht(),v=Yt(l,{relative:h}),m=u.toLowerCase()==="get"?"get":"post",x=f=>{if(c&&c(f),f.defaultPrevented)return;f.preventDefault();let g=f.nativeEvent.submitter,b=(g==null?void 0:g.getAttribute("formmethod"))||u;y(g||f.currentTarget,{fetcherKey:n,method:b,navigate:r,replace:i,state:o,relative:h,preventScrollReset:d,viewTransition:p})};return s.createElement("form",_({ref:t,method:m,action:v,onSubmit:a?c:x},w))});$t.displayName="Form";var K;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(K||(K={}));var ve;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(ve||(ve={}));function jt(e){return e+" must be used within a data router.  See https://reactrouter.com/v6/routers/picking-a-router."}function Le(e){let t=s.useContext(W);return t||R(!1,jt(e)),t}function Vt(e,t){let{target:n,replace:r,state:a,preventScrollReset:i,relative:o,viewTransition:u}=t===void 0?{}:t,l=we(),c=U(),h=$(e,{relative:o});return s.useCallback(d=>{if(Nt(d,n)){d.preventDefault();let p=r!==void 0?r:k(c)===k(h);l(e,{replace:p,state:a,preventScrollReset:i,relative:o,viewTransition:u})}},[c,l,h,r,a,n,e,i,o,u])}function zt(){if(typeof document>"u")throw new Error("You are calling submit during the server render. Try calling submit within a `useEffect` or callback instead.")}let Jt=0,Kt=()=>"__"+String(++Jt)+"__";function Ht(){let{router:e}=Le(K.UseSubmit),{basename:t}=s.useContext(C),n=gt();return s.useCallback(function(r,a){a===void 0&&(a={}),zt();let{action:i,method:o,encType:u,formData:l,body:c}=Ot(r,t);if(a.navigate===!1){let h=a.fetcherKey||Kt();e.fetch(h,n,a.action||i,{preventScrollReset:a.preventScrollReset,formData:l,body:c,formMethod:a.method||o,formEncType:a.encType||u,flushSync:a.flushSync})}else e.navigate(a.action||i,{preventScrollReset:a.preventScrollReset,formData:l,body:c,formMethod:a.method||o,formEncType:a.encType||u,replace:a.replace,state:a.state,fromRouteId:n,flushSync:a.flushSync,viewTransition:a.viewTransition})},[e,t,n])}function Yt(e,t){let{relative:n}=t===void 0?{}:t,{basename:r}=s.useContext(C),a=s.useContext(S);a||R(!1,"useFormAction must be used inside a RouteContext");let[i]=a.matches.slice(-1),o=_({},$(e||".",{relative:n})),u=U();if(e==null){o.search=u.search;let l=new URLSearchParams(o.search),c=l.getAll("index");if(c.some(d=>d==="")){l.delete("index"),c.filter(p=>p).forEach(p=>l.append("index",p));let d=l.toString();o.search=d?"?"+d:""}}return(!e||e===".")&&i.route.index&&(o.search=o.search?o.search.replace(/^\?/,"?index&"):"?index"),r!=="/"&&(o.pathname=o.pathname==="/"?r:P([r,o.pathname])),k(o)}function qt(e,t){t===void 0&&(t={});let n=s.useContext(Se);n==null&&R(!1,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:r}=Le(K.useViewTransitionState),a=$(e,{relative:t.relative});if(!n.isTransitioning)return!1;let i=T(n.currentLocation.pathname,r)||n.currentLocation.pathname,o=T(n.nextLocation.pathname,r)||n.nextLocation.pathname;return J(a.pathname,o)!=null||J(a.pathname,i)!=null}export{en as B,Pe as L,Xt as N,Qt as O,Zt as R,we as a,wt as b,U as u};
