import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

// 配置 Supabase 客户端
const supabaseUrl = process.env.SUPABASE_URL?.trim();
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY?.trim();

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('请设置 SUPABASE_URL 和 SUPABASE_SERVICE_ROLE_KEY 环境变量');
  console.error('当前 SUPABASE_URL:', process.env.SUPABASE_URL);
  console.error('当前 SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? '已设置' : '未设置');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// 读取现有数据文件
function loadExistingData() {
  try {
    const dataPath = path.join(process.cwd(), 'server', 'data.json');
    if (fs.existsSync(dataPath)) {
      const rawData = fs.readFileSync(dataPath, 'utf8');
      return JSON.parse(rawData);
    }
    return null;
  } catch (error) {
    console.error('读取现有数据失败:', error);
    return null;
  }
}

// 迁移用户数据
async function migrateUsers(existingData) {
  console.log('开始迁移用户数据...');
  
  if (!existingData || !existingData.users) {
    console.log('没有找到用户数据，跳过用户迁移');
    return;
  }

  const users = existingData.users;
  let successCount = 0;
  let errorCount = 0;

  for (const user of users) {
    try {
      // 检查用户是否已存在
      const { data: existingUser } = await supabase
        .from('users')
        .select('id')
        .eq('student_id', user.student_id)
        .single();

      if (existingUser) {
        console.log(`用户 ${user.student_id} 已存在，跳过`);
        continue;
      }

      // 哈希密码
      const passwordHash = await bcrypt.hash(user.password || user.student_id, 10);

      // 插入用户数据
      const { error } = await supabase
        .from('users')
        .insert({
          student_id: user.student_id,
          username: user.username || user.student_id,
          name: user.name,
          class_name: user.class_name,
          password_hash: passwordHash,
          role: user.role || 'student',
          permissions: user.permissions || [],
          is_super_admin: user.is_super_admin || false,
          created_at: user.created_at || new Date().toISOString(),
          last_login: user.last_login
        });

      if (error) {
        console.error(`迁移用户 ${user.student_id} 失败:`, error);
        errorCount++;
      } else {
        console.log(`成功迁移用户: ${user.name} (${user.student_id})`);
        successCount++;
      }
    } catch (error) {
      console.error(`处理用户 ${user.student_id} 时出错:`, error);
      errorCount++;
    }
  }

  console.log(`用户迁移完成: 成功 ${successCount} 个，失败 ${errorCount} 个`);
}

// 迁移证书类别数据
async function migrateCertificateCategories() {
  console.log('开始迁移证书类别数据...');

  const categories = [
    {
      category_name: '竞赛获奖',
      type: 'competition',
      max_score: 100,
      levels: {
        '国家级一等奖': 100,
        '国家级二等奖': 90,
        '国家级三等奖': 80,
        '省级一等奖': 70,
        '省级二等奖': 60,
        '省级三等奖': 50,
        '市级一等奖': 40,
        '市级二等奖': 30,
        '市级三等奖': 20
      },
      requires_team_info: true,
      requires_role_info: false,
      submission_limit: 5
    },
    {
      category_name: '技能证书',
      type: 'skill',
      max_score: 80,
      levels: {
        '国家级': 80,
        '省级': 60,
        '市级': 40,
        '校级': 20
      },
      requires_team_info: false,
      requires_role_info: false,
      submission_limit: 3
    },
    {
      category_name: '创新项目',
      type: 'innovation',
      max_score: 120,
      levels: {
        '国家级': 120,
        '省级': 100,
        '市级': 80,
        '校级': 60
      },
      requires_team_info: true,
      requires_role_info: true,
      submission_limit: 2
    }
  ];

  let successCount = 0;
  let errorCount = 0;

  for (const category of categories) {
    try {
      // 检查类别是否已存在
      const { data: existing } = await supabase
        .from('certificate_categories')
        .select('id')
        .eq('category_name', category.category_name)
        .single();

      if (existing) {
        console.log(`证书类别 ${category.category_name} 已存在，跳过`);
        continue;
      }

      const { error } = await supabase
        .from('certificate_categories')
        .insert(category);

      if (error) {
        console.error(`迁移证书类别 ${category.category_name} 失败:`, error);
        errorCount++;
      } else {
        console.log(`成功迁移证书类别: ${category.category_name}`);
        successCount++;
      }
    } catch (error) {
      console.error(`处理证书类别 ${category.category_name} 时出错:`, error);
      errorCount++;
    }
  }

  console.log(`证书类别迁移完成: 成功 ${successCount} 个，失败 ${errorCount} 个`);
}

// 迁移证书数据
async function migrateCertificates(existingData) {
  console.log('开始迁移证书数据...');
  
  if (!existingData || !existingData.certificates) {
    console.log('没有找到证书数据，跳过证书迁移');
    return;
  }

  const certificates = existingData.certificates;
  let successCount = 0;
  let errorCount = 0;

  for (const cert of certificates) {
    try {
      // 获取用户ID
      const { data: userData } = await supabase
        .from('users')
        .select('id')
        .eq('student_id', cert.student_id)
        .single();

      if (!userData) {
        console.error(`找不到学生 ${cert.student_id}，跳过证书 ${cert.certificate_name}`);
        errorCount++;
        continue;
      }

      // 获取证书类别ID
      const { data: categoryData } = await supabase
        .from('certificate_categories')
        .select('id')
        .eq('category_name', cert.category || '竞赛获奖')
        .single();

      if (!categoryData) {
        console.error(`找不到证书类别，跳过证书 ${cert.certificate_name}`);
        errorCount++;
        continue;
      }

      // 插入证书数据
      const { error } = await supabase
        .from('certificates')
        .insert({
          user_id: userData.id,
          category_id: categoryData.id,
          certificate_name: cert.certificate_name,
          level: cert.level,
          score: cert.score || 0,
          award_date: cert.award_date || new Date().toISOString().split('T')[0],
          team_size: cert.team_size || 1,
          role: cert.role || 'individual',
          file_path: cert.file_path,
          file_url: cert.file_url,
          status: cert.status || 'pending',
          audit_comment: cert.audit_comment,
          audited_by: cert.audited_by,
          audited_at: cert.audited_at,
          created_at: cert.created_at || new Date().toISOString()
        });

      if (error) {
        console.error(`迁移证书 ${cert.certificate_name} 失败:`, error);
        errorCount++;
      } else {
        console.log(`成功迁移证书: ${cert.certificate_name}`);
        successCount++;
      }
    } catch (error) {
      console.error(`处理证书 ${cert.certificate_name} 时出错:`, error);
      errorCount++;
    }
  }

  console.log(`证书迁移完成: 成功 ${successCount} 个，失败 ${errorCount} 个`);
}

// 迁移操作日志
async function migrateOperationLogs(existingData) {
  console.log('开始迁移操作日志...');
  
  if (!existingData || !existingData.operation_logs) {
    console.log('没有找到操作日志，跳过日志迁移');
    return;
  }

  const logs = existingData.operation_logs;
  let successCount = 0;
  let errorCount = 0;

  for (const log of logs) {
    try {
      // 获取用户ID（如果有的话）
      let userId = null;
      if (log.student_id) {
        const { data: userData } = await supabase
          .from('users')
          .select('id')
          .eq('student_id', log.student_id)
          .single();
        
        userId = userData?.id;
      }

      const { error } = await supabase
        .from('operation_logs')
        .insert({
          user_id: userId,
          action: log.action,
          details: log.details || {},
          target_type: log.target_type || 'unknown',
          target_id: log.target_id,
          ip_address: log.ip_address,
          user_agent: log.user_agent,
          created_at: log.created_at || new Date().toISOString()
        });

      if (error) {
        console.error(`迁移操作日志失败:`, error);
        errorCount++;
      } else {
        successCount++;
      }
    } catch (error) {
      console.error(`处理操作日志时出错:`, error);
      errorCount++;
    }
  }

  console.log(`操作日志迁移完成: 成功 ${successCount} 个，失败 ${errorCount} 个`);
}

// 主迁移函数
async function main() {
  console.log('🚀 开始数据迁移...');
  console.log('📊 Supabase URL:', supabaseUrl);
  console.log('🔑 Service Key:', supabaseServiceKey ? '已设置' : '未设置');

  try {
    // 测试 Supabase 连接
    console.log('🔍 测试 Supabase 连接...');
    const { data, error } = await supabase.from('users').select('count').limit(1);
    if (error) {
      console.error('❌ Supabase 连接失败:', error);
      process.exit(1);
    }
    console.log('✅ Supabase 连接成功');

    // 加载现有数据
    const existingData = loadExistingData();
    
    if (!existingData) {
      console.log('没有找到现有数据文件，将只创建基础数据');
    }

    // 执行迁移
    await migrateCertificateCategories();
    await migrateUsers(existingData);
    await migrateCertificates(existingData);
    await migrateOperationLogs(existingData);

    console.log('数据迁移完成！');
    
  } catch (error) {
    console.error('迁移过程中发生错误:', error);
    process.exit(1);
  }
}

// 运行迁移
main().catch(error => {
  console.error('迁移失败:', error);
  process.exit(1);
});
