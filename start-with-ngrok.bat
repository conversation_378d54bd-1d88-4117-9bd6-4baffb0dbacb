@echo off
chcp 65001 >nul
title 学生素质测评系统 - 内网穿透部署

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║              学生素质测评系统 - 内网穿透部署                 ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 检查ngrok是否已安装...
ngrok version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到ngrok，请先安装ngrok
    echo 📥 下载地址: https://ngrok.com/download
    echo 📖 安装指南: https://ngrok.com/docs/getting-started
    pause
    exit /b 1
)

echo ✅ ngrok已安装

echo.
echo 🚀 正在启动本地服务器...
start "学生素质测评系统服务器" cmd /k "cd server && node sqlite-server.js"

echo ⏳ 等待服务器启动...
timeout /t 5 /nobreak >nul

echo.
echo 🌐 正在启动内网穿透...
echo 📝 ngrok控制台: http://localhost:4040
echo.

ngrok http 5000

pause
