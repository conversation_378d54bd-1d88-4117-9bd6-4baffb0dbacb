import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import apiService from '../../services/api';
import toast from 'react-hot-toast';

const Announcements = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [announcements, setAnnouncements] = useState([]);
  const [selectedAnnouncement, setSelectedAnnouncement] = useState(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [filter, setFilter] = useState('all');

  useEffect(() => {
    fetchAnnouncements();
  }, []);

  const fetchAnnouncements = async () => {
    try {
      setLoading(true);
      
      // Get announcements for students
      const announcements = await apiService.announcements.getForStudent();
      setAnnouncements(announcements);
    } catch (error) {
      console.error('Error fetching announcements:', error);
      toast.error('获取公告列表失败');

      // Fallback to mock data
      setAnnouncements([
        {
          id: 1,
          title: '证书提交截止时间提醒',
          content: '请各位同学注意，本学期证书提交将于2024年12月31日截止，请及时提交相关材料。\n\n具体要求：\n1. 所有证书必须为原件扫描件\n2. 图片清晰，格式为JPG或PNG\n3. 文件大小不超过5MB\n4. 证书信息填写完整准确\n\n如有疑问，请及时联系任课教师。',
          type: 'important',
          priority: 'high',
          status: 'active',
          created_at: '2024-07-01T10:00:00Z',
          expires_at: '2024-12-31T23:59:59Z',
          author_name: '系统管理员',
          is_read: false,
          is_important: true
        },
        {
          id: 2,
          title: '系统维护通知',
          content: '系统将于本周六晚上22:00-24:00进行维护升级，期间可能无法正常访问，请提前安排好相关工作。\n\n维护内容：\n- 系统性能优化\n- 新功能上线\n- 安全补丁更新\n\n维护期间如有紧急情况，请联系技术支持。',
          type: 'system',
          priority: 'normal',
          status: 'active',
          created_at: '2024-07-05T14:30:00Z',
          expires_at: '2024-07-08T00:00:00Z',
          author_name: '技术部',
          is_read: true,
          is_important: false
        },
        {
          id: 3,
          title: '学期末成绩统计说明',
          content: '各位同学，学期末成绩统计工作即将开始，请注意以下事项：\n\n1. 确保所有证书材料已提交完整\n2. 核对个人信息是否准确\n3. 关注成绩公布时间\n4. 如有异议请及时申诉\n\n成绩计算方式：\n- 课程成绩占80%\n- 素质测评占20%\n\n详细计算规则请查看系统说明。',
          type: 'general',
          priority: 'normal',
          status: 'active',
          created_at: '2024-07-03T09:15:00Z',
          expires_at: null,
          author_name: '教务处',
          is_read: true,
          is_important: false
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleViewDetail = async (announcement) => {
    setSelectedAnnouncement(announcement);
    setShowDetailModal(true);

    // Mark as read if not already read
    if (!announcement.is_read) {
      try {
        await apiService.announcements.markAsRead(announcement.id);
        // Update local state
        setAnnouncements(prev => 
          prev.map(ann => 
            ann.id === announcement.id 
              ? { ...ann, is_read: true }
              : ann
          )
        );
      } catch (error) {
        console.error('Error marking announcement as read:', error);
      }
    }
  };

  const getTypeLabel = (type) => {
    const types = {
      general: '一般通知',
      important: '重要通知',
      system: '系统通知',
      urgent: '紧急通知'
    };
    return types[type] || type;
  };

  const getPriorityBadge = (priority) => {
    const badges = {
      low: 'bg-gray-100 text-gray-800',
      normal: 'bg-blue-100 text-blue-800',
      high: 'bg-orange-100 text-orange-800',
      urgent: 'bg-red-100 text-red-800'
    };
    const labels = {
      low: '低',
      normal: '普通',
      high: '高',
      urgent: '紧急'
    };
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${badges[priority]}`}>
        {labels[priority]}
      </span>
    );
  };

  const getFilteredAnnouncements = () => {
    switch (filter) {
      case 'unread':
        return announcements.filter(ann => !ann.is_read);
      case 'important':
        return announcements.filter(ann => ann.priority === 'high' || ann.priority === 'urgent');
      case 'system':
        return announcements.filter(ann => ann.type === 'system');
      default:
        return announcements;
    }
  };

  const filteredAnnouncements = getFilteredAnnouncements();

  if (loading) {
    return <LoadingSpinner text="加载公告中..." />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">系统公告</h1>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">
            未读: {announcements.filter(ann => !ann.is_read).length}
          </span>
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="bg-white shadow rounded-lg">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {[
              { key: 'all', label: '全部公告', count: announcements.length },
              { key: 'unread', label: '未读消息', count: announcements.filter(ann => !ann.is_read).length },
              { key: 'important', label: '重要通知', count: announcements.filter(ann => ann.priority === 'high' || ann.priority === 'urgent').length },
              { key: 'system', label: '系统通知', count: announcements.filter(ann => ann.type === 'system').length }
            ].map(tab => (
              <button
                key={tab.key}
                onClick={() => setFilter(tab.key)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  filter === tab.key
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
                {tab.count > 0 && (
                  <span className={`ml-2 py-0.5 px-2 rounded-full text-xs ${
                    filter === tab.key
                      ? 'bg-blue-100 text-blue-600'
                      : 'bg-gray-100 text-gray-600'
                  }`}>
                    {tab.count}
                  </span>
                )}
              </button>
            ))}
          </nav>
        </div>

        {/* Announcements List */}
        <div className="p-6">
          {filteredAnnouncements.length === 0 ? (
            <div className="text-center py-8">
              <span className="text-4xl mb-4 block">📢</span>
              <p className="text-gray-500">
                {filter === 'all' ? '暂无公告' : 
                 filter === 'unread' ? '暂无未读消息' :
                 filter === 'important' ? '暂无重要通知' : '暂无系统通知'}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredAnnouncements.map(announcement => (
                <div 
                  key={announcement.id} 
                  className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                    announcement.is_read 
                      ? 'border-gray-200 hover:border-gray-300' 
                      : 'border-blue-200 bg-blue-50 hover:border-blue-300'
                  }`}
                  onClick={() => handleViewDetail(announcement)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        {!announcement.is_read && (
                          <span className="w-2 h-2 bg-blue-600 rounded-full"></span>
                        )}
                        <h3 className={`text-lg font-medium ${
                          announcement.is_read ? 'text-gray-900' : 'text-blue-900'
                        }`}>
                          {announcement.title}
                        </h3>
                        {getPriorityBadge(announcement.priority)}
                        {(announcement.priority === 'high' || announcement.priority === 'urgent') && (
                          <span className="text-red-500">🔥</span>
                        )}
                      </div>
                      
                      <p className="text-gray-600 mb-3 line-clamp-2">
                        {announcement.content.split('\n')[0]}
                      </p>
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>类型：{getTypeLabel(announcement.type)}</span>
                        <span>发布时间：{new Date(announcement.created_at).toLocaleString()}</span>
                        {announcement.expires_at && (
                          <span className="text-orange-600">
                            过期时间：{new Date(announcement.expires_at).toLocaleString()}
                          </span>
                        )}
                      </div>
                    </div>
                    
                    <div className="ml-4">
                      <span className="text-gray-400">→</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Detail Modal */}
      {showDetailModal && selectedAnnouncement && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-full max-w-3xl shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h3 className="text-xl font-bold text-gray-900">{selectedAnnouncement.title}</h3>
                    {getPriorityBadge(selectedAnnouncement.priority)}
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <span>类型：{getTypeLabel(selectedAnnouncement.type)}</span>
                    <span>发布者：{selectedAnnouncement.author_name}</span>
                    <span>发布时间：{new Date(selectedAnnouncement.created_at).toLocaleString()}</span>
                  </div>
                </div>
                <button
                  onClick={() => setShowDetailModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>
              
              <div className="border-t pt-4">
                <div className="prose max-w-none">
                  {selectedAnnouncement.content.split('\n').map((line, index) => (
                    <p key={index} className="mb-2 text-gray-700">
                      {line || '\u00A0'}
                    </p>
                  ))}
                </div>
              </div>

              {selectedAnnouncement.expires_at && (
                <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                  <div className="flex items-center">
                    <span className="text-orange-600 mr-2">⏰</span>
                    <span className="text-sm text-orange-800">
                      此公告将于 {new Date(selectedAnnouncement.expires_at).toLocaleString()} 过期
                    </span>
                  </div>
                </div>
              )}

              <div className="flex justify-end pt-4">
                <button
                  onClick={() => setShowDetailModal(false)}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
                >
                  关闭
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Announcements;
