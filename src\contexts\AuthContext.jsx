import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase, db } from '../services/supabase';
import toast from 'react-hot-toast';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if user is logged in on app start
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      // Check for stored session
      const authToken = localStorage.getItem('auth_token');
      const userData = localStorage.getItem('user_data');

      if (authToken && userData) {
        try {
          const user = JSON.parse(userData);
          setUser(user);
        } catch (parseError) {
          console.error('Failed to parse user data:', parseError);
          localStorage.removeItem('auth_token');
          localStorage.removeItem('user_data');
        }
      }
    } catch (error) {
      console.error('Auth check failed:', error);
    } finally {
      setLoading(false);
    }
  };



  const login = async (studentIdOrCredentials, password = null) => {
    try {
      setLoading(true);

      // Handle both old and new calling patterns
      let credentials;
      if (typeof studentIdOrCredentials === 'string') {
        // Old pattern: login(studentId, password)
        credentials = {
          username: studentIdOrCredentials,
          password: password
        };
      } else {
        // New pattern: login(credentials)
        credentials = studentIdOrCredentials;
      }

      // Validate credentials
      if (!credentials || !credentials.username || !credentials.password) {
        return { success: false, message: '请输入账号和密码' };
      }

      // Call our login API endpoint directly
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          student_id: credentials.username,
          password: credentials.password,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        console.error('Login API error:', result);
        return { success: false, message: result.message || '登录失败' };
      }

      // Store session info
      if (result.session && result.session.access_token) {
        localStorage.setItem('auth_token', result.session.access_token);
        localStorage.setItem('user_data', JSON.stringify(result.user));
      }

      // Set user data
      setUser(result.user);
      toast.success('登录成功！');
      return { success: true, user: result.user };

    } catch (error) {
      console.error('Login error:', error);
      return { success: false, message: '网络错误，请稍后重试' };
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      // Clear local storage
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_data');

      // Clear user state
      setUser(null);
      toast.success('已安全退出');
    } catch (error) {
      console.error('Logout error:', error);
      setUser(null);
    }
  };



  const changePassword = async (passwordData) => {
    try {
      if (!user) {
        return { success: false, message: '用户未登录' };
      }

      // Verify old password first
      if (user.password_hash !== passwordData.oldPassword) {
        return { success: false, message: '原密码错误' };
      }

      // Update password in database
      await db.users.update(user.id, {
        password_hash: passwordData.newPassword,
        updated_at: new Date().toISOString()
      });

      // Update password in Supabase Auth
      const { error } = await supabase.auth.updateUser({
        password: passwordData.newPassword
      });

      if (error) {
        throw error;
      }

      // Log the password change
      await db.logs.create({
        user_id: user.id,
        action: '密码修改',
        details: {
          user_role: user.role,
          user_name: user.name,
        },
        target_type: 'auth',
      });

      toast.success('密码修改成功');
      return { success: true, message: '密码修改成功' };
    } catch (error) {
      console.error('Change password error:', error);
      return { success: false, message: error.message || '密码修改失败' };
    }
  };

  const updateProfile = async (profileData) => {
    try {
      if (!user) {
        return { success: false, message: '用户未登录' };
      }

      // Update user profile in database
      const updatedUser = await db.users.update(user.id, {
        ...profileData,
        updated_at: new Date().toISOString()
      });

      // Log the profile update
      await db.logs.create({
        user_id: user.id,
        action: '个人信息修改',
        details: {
          updated_fields: Object.keys(profileData),
          user_role: user.role,
          user_name: user.name,
        },
        target_type: 'profile',
      });

      setUser(updatedUser);
      toast.success('个人信息更新成功');
      return { success: true, user: updatedUser };
    } catch (error) {
      console.error('Update profile error:', error);
      return { success: false, message: error.message || '更新失败' };
    }
  };

  const refreshUser = async () => {
    try {
      if (!user) return;

      // Refresh user data from database
      const refreshedUser = await db.users.getById(user.id);
      setUser(refreshedUser);
    } catch (error) {
      console.error('Refresh user error:', error);
    }
  };

  const value = {
    user,
    loading,
    login,
    logout,
    changePassword,
    updateProfile,
    refreshUser,
    checkAuthStatus
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
