{
  "name": "student-assessment-system",
  "version": "2.0.1",
  "description": "学生素质测评系统",
  "type": "module",
  "engines": {
    "node": ">=18.0.0"
  },
  "scripts": {
<<<<<<< HEAD
    "start": "cd server && npm start",
    "dev": "cd server && npm run dev",
    "install-server": "cd server && npm install",
    "install-client": "cd client && npm install",
    "build": "npm run install-server && npm run install-client && npm run build-client",
    "build-client": "cd client && npm run build",
    "postinstall": "npm run install-server && npm run install-client",
    "deploy": "npm run build && npm start"
  },
  "dependencies": {
    "axios": "^1.10.0"
  },
  "workspaces": [
    "server",
    "client"
  ]
=======
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview",
    "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0"
  },
  "dependencies": {
    "@headlessui/react": "^1.7.17",
    "@heroicons/react": "^2.0.18",
    "@supabase/supabase-js": "^2.50.3",
    "@tailwindcss/forms": "^0.5.7",
    "@vitejs/plugin-react": "^4.1.1",
    "autoprefixer": "^10.4.16",
    "bcryptjs": "^3.0.2",
    "date-fns": "^2.30.0",
    "dotenv": "^17.0.1",
    "lucide-react": "^0.294.0",
    "node-fetch": "^3.3.2",
    "postcss": "^8.4.31",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-hook-form": "^7.48.2",
    "react-hot-toast": "^2.5.2",
    "react-router-dom": "^6.30.1",
    "recharts": "^2.8.0",
    "tailwindcss": "^3.3.5",
    "uuid": "^9.0.1",
    "vite": "^4.5.0",
    "xlsx": "^0.18.5"
  },
  "devDependencies": {
    "@types/react": "^18.2.37",
    "@types/react-dom": "^18.2.15",
    "eslint": "^8.53.0",
    "eslint-plugin-react": "^7.33.2",
    "eslint-plugin-react-hooks": "^4.6.0",
    "eslint-plugin-react-refresh": "^0.4.4"
  },
  "repository": {
    "type": "git",
    "url": "https://github.com/ruiyan886/henurjxylyhsanping.git"
  },
  "keywords": [
    "student-assessment",
    "certificate-management",
    "react",
    "supabase",
    "vercel"
  ],
  "author": "河南大学体育学院",
  "license": "MIT"
>>>>>>> 3450bba2beb2d0d0982e5e542bfe59998c90ff0b
}
