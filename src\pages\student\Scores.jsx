import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import reportsService from '../../services/reports';
import toast from 'react-hot-toast';

const Scores = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [scoreData, setScoreData] = useState({
    totalScore: 0,
    finalScore: 0,
    baseScore: 25,
    categories: [],
    certificates: []
  });

  useEffect(() => {
    fetchScoreData();
  }, []);

  const fetchScoreData = async () => {
    try {
      setLoading(true);

      // Get student score data using reports service
      const data = await reportsService.getStudentScoreData(user.id);
      setScoreData(data);
    } catch (error) {
      console.error('Error fetching score data:', error);
      toast.error('获取成绩数据失败');

      // Fallback to mock data
      setScoreData({
        totalScore: 85.5,
        finalScore: 17.1, // totalScore * 0.2
        baseScore: 25,
        categories: [
          {
            id: 1,
            category_name: '专业类活动',
            max_score: 30,
            current_score: 25.0,
            certificate_count: 3
          },
          {
            id: 2,
            category_name: '体育美育综合活动',
            max_score: 20,
            current_score: 18.5,
            certificate_count: 2
          },
          {
            id: 3,
            category_name: '文明品德综合活动',
            max_score: 25,
            current_score: 22.0,
            certificate_count: 4
          }
        ],
        certificates: [
          {
            id: 1,
            certificate_name: '全国大学生数学建模竞赛',
            category_name: '专业类活动',
            level: '省级一等奖',
            score: 8,
            status: 'approved',
            award_date: '2024-10-15'
          },
          {
            id: 2,
            certificate_name: '程序设计竞赛',
            category_name: '专业类活动',
            level: '校级二等奖',
            score: 5,
            status: 'approved',
            award_date: '2024-09-20'
          },
          {
            id: 3,
            certificate_name: '羽毛球比赛',
            category_name: '体育美育综合活动',
            level: '校级第一名',
            score: 5,
            status: 'approved',
            award_date: '2024-11-01'
          },
          {
            id: 4,
            certificate_name: '志愿服务活动',
            category_name: '文明品德综合活动',
            level: '校级优秀志愿者',
            score: 5,
            status: 'approved',
            award_date: '2024-10-10'
          }
        ]
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <LoadingSpinner text="加载成绩数据中..." />;
  }

  const ScoreCard = ({ title, score, maxScore, icon, color = 'blue' }) => {
    const percentage = maxScore ? (score / maxScore) * 100 : 0;
    
    return (
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="p-5">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <span className="text-2xl">{icon}</span>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  {title}
                </dt>
                <dd className={`text-lg font-medium text-${color}-600`}>
                  {score}{maxScore ? `/${maxScore}` : ''}分
                </dd>
                {maxScore && (
                  <div className="mt-2">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`bg-${color}-600 h-2 rounded-full`} 
                        style={{ width: `${Math.min(percentage, 100)}%` }}
                      ></div>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      完成度: {percentage.toFixed(1)}%
                    </p>
                  </div>
                )}
              </dl>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">成绩查看</h1>
        <button
          onClick={fetchScoreData}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          🔄 刷新数据
        </button>
      </div>

      {/* Overall Score */}
      <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg shadow-lg text-white p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-medium opacity-90">综合素质测评成绩</h2>
            <p className="text-3xl font-bold mt-2">{scoreData.finalScore}分</p>
            <p className="text-sm opacity-75 mt-1">
              总分值: {scoreData.totalScore}分 × 20% = {scoreData.finalScore}分
            </p>
          </div>
          <div className="text-right">
            <span className="text-4xl">🏆</span>
            <p className="text-sm opacity-75 mt-2">
              基础分: {scoreData.baseScore}分
            </p>
          </div>
        </div>
      </div>

      {/* Category Scores */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-3">
        {scoreData.categories.map((category, index) => {
          const colors = ['blue', 'green', 'purple'];
          const icons = ['📚', '🏃‍♂️', '🌟'];
          
          return (
            <ScoreCard
              key={category.id}
              title={category.category_name}
              score={category.current_score}
              maxScore={category.max_score}
              icon={icons[index]}
              color={colors[index]}
            />
          );
        })}
      </div>

      {/* Score Breakdown */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            成绩构成详情
          </h3>
          
          {/* Base Score */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <span className="text-lg mr-3">📋</span>
                <div>
                  <h4 className="font-medium text-gray-900">基础分值</h4>
                  <p className="text-sm text-gray-600">
                    无违纪现象，遵守院纪校规，积极参与各项活动
                  </p>
                </div>
              </div>
              <span className="text-lg font-semibold text-gray-900">
                {scoreData.baseScore}分
              </span>
            </div>
          </div>

          {/* Category Details */}
          <div className="space-y-4">
            {scoreData.categories.map((category, index) => {
              const categoryColors = ['blue', 'green', 'purple'];
              const categoryIcons = ['📚', '🏃‍♂️', '🌟'];
              const categoryCerts = scoreData.certificates.filter(
                cert => cert.category_name === category.category_name && cert.status === 'approved'
              );
              
              return (
                <div key={category.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center">
                      <span className="text-lg mr-3">{categoryIcons[index]}</span>
                      <div>
                        <h4 className="font-medium text-gray-900">{category.category_name}</h4>
                        <p className="text-sm text-gray-600">
                          {categoryCerts.length}个证书 · 封顶{category.max_score}分
                        </p>
                      </div>
                    </div>
                    <span className={`text-lg font-semibold text-${categoryColors[index]}-600`}>
                      {category.current_score}/{category.max_score}分
                    </span>
                  </div>
                  
                  {categoryCerts.length > 0 && (
                    <div className="ml-8 space-y-2">
                      {categoryCerts.map(cert => (
                        <div key={cert.id} className="flex items-center justify-between text-sm">
                          <div>
                            <span className="font-medium text-gray-700">{cert.certificate_name}</span>
                            <span className="text-gray-500 ml-2">({cert.level})</span>
                          </div>
                          <span className="font-medium text-gray-900">+{cert.score}分</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Score History */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            证书得分记录
          </h3>
          
          {scoreData.certificates.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <span className="text-4xl mb-4 block">📜</span>
              <p>暂无证书记录</p>
            </div>
          ) : (
            <div className="space-y-3">
              {scoreData.certificates.map(cert => (
                <div key={cert.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center">
                    <span className="text-lg mr-3">🏆</span>
                    <div>
                      <p className="font-medium text-gray-900">{cert.certificate_name}</p>
                      <p className="text-sm text-gray-600">
                        {cert.category_name} · {cert.level}
                      </p>
                      <p className="text-xs text-gray-500">
                        获奖日期: {new Date(cert.award_date).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <span className="text-lg font-semibold text-green-600">+{cert.score}分</span>
                    <p className="text-xs text-gray-500">已通过</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Score Calculation Rules */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-900 mb-2">📖 成绩计算说明</h4>
        <div className="text-sm text-blue-800 space-y-1">
          <p>• 基础分值：25分（无违纪现象，积极参与各项活动）</p>
          <p>• 专业类活动：累计加分，封顶30分</p>
          <p>• 体育美育综合活动：累计加分，封顶20分</p>
          <p>• 文明品德综合活动：累计加分，封顶25分</p>
          <p>• 最终成绩：总分值 × 20%</p>
        </div>
      </div>
    </div>
  );
};

export default Scores;
