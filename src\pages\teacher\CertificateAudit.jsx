import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import apiService from '../../services/api';
import toast from 'react-hot-toast';

const CertificateAudit = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [certificates, setCertificates] = useState([]);
  const [filter, setFilter] = useState('pending'); // pending, all, approved, rejected
  const [selectedCert, setSelectedCert] = useState(null);
  const [showAuditModal, setShowAuditModal] = useState(false);
  const [auditForm, setAuditForm] = useState({
    status: '',
    score: '',
    comments: ''
  });
  const [auditing, setAuditing] = useState(false);

  // Batch audit states
  const [selectedCerts, setSelectedCerts] = useState([]);
  const [showBatchAuditModal, setShowBatchAuditModal] = useState(false);
  const [batchAuditForm, setBatchAuditForm] = useState({
    status: '',
    score: '',
    comments: ''
  });
  const [batchAuditing, setBatchAuditing] = useState(false);

  useEffect(() => {
    fetchCertificates();
  }, [filter]);

  const fetchCertificates = async () => {
    try {
      setLoading(true);
      
      // Get certificates using API service
      const filters = filter === 'all' ? {} : { status: filter };
      const certificates = await apiService.certificates.getAll(filters);
      setCertificates(certificates);
    } catch (error) {
      console.error('Error fetching certificates:', error);
      toast.error('获取证书列表失败');

      // Fallback to mock data
      const mockData = [
        {
          id: 1,
          student_name: '张三',
          student_id: '2021001',
          certificate_name: '全国大学生数学建模竞赛',
          category_name: '学科竞赛',
          level: '省级一等奖',
          score: 8,
          status: 'pending',
          award_date: '2024-10-15',
          file_url: '/uploads/cert1.jpg',
          submitted_at: '2024-10-20T10:30:00Z',
          team_size: 3,
          role: '队长'
        },
        {
          id: 2,
          student_name: '李四',
          student_id: '2021002',
          certificate_name: '羽毛球比赛',
          category_name: '体育竞赛',
          level: '校级第一名',
          score: 5,
          status: 'pending',
          award_date: '2024-11-01',
          file_url: '/uploads/cert2.jpg',
          submitted_at: '2024-11-05T14:20:00Z'
        },
        {
          id: 3,
          student_name: '王五',
          student_id: '2021003',
          certificate_name: '程序设计竞赛',
          category_name: '学科竞赛',
          level: '校级二等奖',
          score: 5,
          status: 'approved',
          award_date: '2024-09-20',
          file_url: '/uploads/cert3.jpg',
          submitted_at: '2024-09-25T09:15:00Z',
          audited_at: '2024-09-26T11:30:00Z',
          auditor_comments: '证书真实有效，符合加分标准'
        }
      ];

      // Filter based on current filter
      const filteredData = filter === 'all' ? mockData : mockData.filter(cert => cert.status === filter);
      setCertificates(filteredData);
    } finally {
      setLoading(false);
    }
  };

  const handleAuditClick = (cert) => {
    setSelectedCert(cert);
    setAuditForm({
      status: '',
      score: cert.score.toString(),
      comments: ''
    });
    setShowAuditModal(true);
  };

  const handleAuditSubmit = async (e) => {
    e.preventDefault();

    if (!auditForm.status) {
      toast.error('请选择审核结果');
      return;
    }

    if (auditForm.status === 'approved' && !auditForm.score) {
      toast.error('通过审核时请填写分数');
      return;
    }

    setAuditing(true);
    try {
      // Prepare audit data
      const auditData = {
        status: auditForm.status,
        score: auditForm.status === 'approved' ? parseFloat(auditForm.score) : 0,
        comments: auditForm.comments,
        audited_by: user.id
      };

      // Submit audit using API service
      await apiService.certificates.audit(selectedCert.id, auditData);

      // Close modal and refresh list
      setShowAuditModal(false);
      fetchCertificates();
    } catch (error) {
      console.error('Audit error:', error);
      // Error handling is done in the API service
    } finally {
      setAuditing(false);
    }
  };

  // Handle batch audit
  const handleBatchAudit = () => {
    if (selectedCerts.length === 0) {
      toast.error('请选择要审核的证书');
      return;
    }

    setBatchAuditForm({
      status: '',
      score: '',
      comments: ''
    });
    setShowBatchAuditModal(true);
  };

  const handleBatchAuditSubmit = async (e) => {
    e.preventDefault();

    if (!batchAuditForm.status) {
      toast.error('请选择审核结果');
      return;
    }

    if (batchAuditForm.status === 'approved' && !batchAuditForm.score) {
      toast.error('通过审核时请填写分数');
      return;
    }

    setBatchAuditing(true);
    try {
      // Prepare audit data
      const auditData = {
        status: batchAuditForm.status,
        score: batchAuditForm.status === 'approved' ? parseFloat(batchAuditForm.score) : 0,
        comments: batchAuditForm.comments,
        audited_by: user.id
      };

      // Batch audit using API service
      await apiService.certificates.auditBatch(selectedCerts, auditData);

      // Reset form and close modal
      setShowBatchAuditModal(false);
      setBatchAuditForm({
        status: '',
        score: '',
        comments: ''
      });
      setSelectedCerts([]);

      // Refresh the list
      fetchCertificates();
    } catch (error) {
      console.error('Batch audit error:', error);
      // Error handling is done in the API service
    } finally {
      setBatchAuditing(false);
    }
  };

  // Handle certificate selection
  const handleCertSelection = (certId) => {
    setSelectedCerts(prev =>
      prev.includes(certId)
        ? prev.filter(id => id !== certId)
        : [...prev, certId]
    );
  };

  // Handle select all
  const handleSelectAll = () => {
    const pendingCerts = certificates.filter(cert => cert.status === 'pending');
    if (selectedCerts.length === pendingCerts.length) {
      setSelectedCerts([]);
    } else {
      setSelectedCerts(pendingCerts.map(cert => cert.id));
    }
  };

  const getStatusBadge = (status) => {
    const badges = {
      pending: 'bg-yellow-100 text-yellow-800',
      approved: 'bg-green-100 text-green-800',
      rejected: 'bg-red-100 text-red-800'
    };
    
    const labels = {
      pending: '待审核',
      approved: '已通过',
      rejected: '已拒绝'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${badges[status] || badges.pending}`}>
        {labels[status] || '未知'}
      </span>
    );
  };

  if (loading) {
    return <LoadingSpinner text="加载证书列表中..." />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">证书审核</h1>
        <div className="flex space-x-2">
          {['pending', 'approved', 'rejected', 'all'].map(status => (
            <button
              key={status}
              onClick={() => setFilter(status)}
              className={`px-3 py-1 text-sm rounded-md ${
                filter === status
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {status === 'pending' && '待审核'}
              {status === 'approved' && '已通过'}
              {status === 'rejected' && '已拒绝'}
              {status === 'all' && '全部'}
            </button>
          ))}
        </div>
      </div>

      {/* Batch Operations */}
      {filter === 'pending' && (
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={selectedCerts.length > 0 && selectedCerts.length === certificates.filter(cert => cert.status === 'pending').length}
                  onChange={handleSelectAll}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">全选</span>
              </label>
              {selectedCerts.length > 0 && (
                <span className="text-sm text-gray-600">
                  已选择 {selectedCerts.length} 个证书
                </span>
              )}
            </div>
            {selectedCerts.length > 0 && (
              <button
                onClick={handleBatchAudit}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                批量审核
              </button>
            )}
          </div>
        </div>
      )}

      {/* Statistics */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-4">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">📜</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">总证书数</dt>
                  <dd className="text-lg font-medium text-gray-900">{certificates.length}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">⏳</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">待审核</dt>
                  <dd className="text-lg font-medium text-yellow-600">
                    {certificates.filter(cert => cert.status === 'pending').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">✅</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">已通过</dt>
                  <dd className="text-lg font-medium text-green-600">
                    {certificates.filter(cert => cert.status === 'approved').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">❌</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">已拒绝</dt>
                  <dd className="text-lg font-medium text-red-600">
                    {certificates.filter(cert => cert.status === 'rejected').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Certificates List */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <ul className="divide-y divide-gray-200">
          {certificates.length === 0 ? (
            <li className="px-6 py-8 text-center text-gray-500">
              <span className="text-4xl mb-4 block">📜</span>
              <p>暂无{filter === 'pending' ? '待审核' : ''}证书记录</p>
            </li>
          ) : (
            certificates.map((cert) => (
              <li key={cert.id}>
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      {/* Checkbox for batch selection (only for pending certificates) */}
                      {cert.status === 'pending' && filter === 'pending' && (
                        <div className="flex-shrink-0 mr-3">
                          <input
                            type="checkbox"
                            checked={selectedCerts.includes(cert.id)}
                            onChange={() => handleCertSelection(cert.id)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                        </div>
                      )}
                      <div className="flex-shrink-0">
                        <span className="text-2xl">🏆</span>
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-900 flex items-center gap-2">
                          {cert.certificate_name}
                          {cert.category_name === '文艺活动' && cert.is_custom_input && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                              自定义审核
                            </span>
                          )}
                        </p>
                        <p className="text-sm text-gray-500">
                          {cert.student_name} ({cert.student_id}) · {cert.category_name} · {cert.level}
                        </p>
                        <p className="text-xs text-gray-400">
                          提交时间: {new Date(cert.submitted_at).toLocaleString()}
                        </p>
                        {cert.team_size && (
                          <p className="text-xs text-gray-400">
                            团队规模: {cert.team_size}人 {cert.role && `· 角色: ${cert.role}`}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-900">
                          {cert.score}分
                        </p>
                        <p className="text-sm text-gray-500">
                          {new Date(cert.award_date).toLocaleDateString()}
                        </p>
                      </div>
                      {getStatusBadge(cert.status)}
                      {cert.status === 'pending' && (
                        <button
                          onClick={() => handleAuditClick(cert)}
                          className="bg-blue-600 text-white px-3 py-1 text-sm rounded hover:bg-blue-700"
                        >
                          审核
                        </button>
                      )}
                      {cert.file_url && (
                        <button
                          onClick={() => window.open(cert.file_url, '_blank')}
                          className="text-blue-600 hover:text-blue-800 text-sm underline"
                        >
                          查看文件
                        </button>
                      )}
                    </div>
                  </div>
                  {cert.auditor_comments && (
                    <div className="mt-2 ml-12">
                      <p className="text-sm text-gray-600">
                        <span className="font-medium">审核意见:</span> {cert.auditor_comments}
                      </p>
                    </div>
                  )}
                </div>
              </li>
            ))
          )}
        </ul>
      </div>

      {/* Audit Modal */}
      {showAuditModal && selectedCert && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">审核证书</h3>
              
              <div className="mb-4 p-3 bg-gray-50 rounded">
                <p className="font-medium">{selectedCert.certificate_name}</p>
                <p className="text-sm text-gray-600">
                  {selectedCert.student_name} · {selectedCert.level}
                </p>
              </div>

              <form onSubmit={handleAuditSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">审核结果 *</label>
                  <select
                    value={auditForm.status}
                    onChange={(e) => setAuditForm({...auditForm, status: e.target.value})}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  >
                    <option value="">请选择</option>
                    <option value="approved">通过</option>
                    <option value="rejected">拒绝</option>
                  </select>
                </div>

                {auditForm.status === 'approved' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">分数 *</label>
                    <input
                      type="number"
                      step="0.1"
                      min="0"
                      max="30"
                      value={auditForm.score}
                      onChange={(e) => setAuditForm({...auditForm, score: e.target.value})}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-700">审核意见</label>
                  <textarea
                    value={auditForm.comments}
                    onChange={(e) => setAuditForm({...auditForm, comments: e.target.value})}
                    rows={3}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请填写审核意见..."
                  />
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowAuditModal(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                    disabled={auditing}
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={auditing}
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"
                  >
                    {auditing ? '审核中...' : '确认审核'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Batch Audit Modal */}
      {showBatchAuditModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">批量审核证书</h3>
                <button
                  onClick={() => setShowBatchAuditModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <div className="mb-4">
                <p className="text-sm text-gray-600">
                  将对选中的 {selectedCerts.length} 个证书进行批量审核
                </p>
              </div>

              <form onSubmit={handleBatchAuditSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">审核结果 *</label>
                  <select
                    value={batchAuditForm.status}
                    onChange={(e) => setBatchAuditForm({...batchAuditForm, status: e.target.value})}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  >
                    <option value="">请选择</option>
                    <option value="approved">通过</option>
                    <option value="rejected">拒绝</option>
                  </select>
                </div>

                {batchAuditForm.status === 'approved' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">分数 *</label>
                    <input
                      type="number"
                      step="0.1"
                      min="0"
                      max="30"
                      value={batchAuditForm.score}
                      onChange={(e) => setBatchAuditForm({...batchAuditForm, score: e.target.value})}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                    <p className="text-xs text-gray-500 mt-1">所有选中的证书将使用相同分数</p>
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-700">审核意见</label>
                  <textarea
                    value={batchAuditForm.comments}
                    onChange={(e) => setBatchAuditForm({...batchAuditForm, comments: e.target.value})}
                    rows={3}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请填写审核意见..."
                  />
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowBatchAuditModal(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                    disabled={batchAuditing}
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={batchAuditing}
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"
                  >
                    {batchAuditing ? '批量审核中...' : '确认批量审核'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CertificateAudit;
