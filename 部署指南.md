# 🚀 学生素质测评系统部署指南

## 📋 目录
1. [本地服务器部署（推荐）](#本地服务器部署)
2. [内网穿透方案](#内网穿透方案)
3. [云服务器部署](#云服务器部署)
4. [常见问题解决](#常见问题解决)

---

## 🏠 本地服务器部署（推荐）

### 环境要求
- Windows 10/11 或 macOS/Linux
- Node.js >= 16.0.0
- 稳定的网络连接
- 防火墙权限

### 快速部署步骤

#### 1. 准备环境
```bash
# 检查Node.js版本
node --version
npm --version
```

#### 2. 一键部署
双击运行 `start-production.bat` 文件，或手动执行：

```bash
# 安装依赖
npm install

# 构建前端
cd client
npm run build
cd ..

# 启动生产服务器
cd server
copy .env.production .env
npm start
```

#### 3. 配置防火墙
- Windows: 允许端口5000通过防火墙
- 路由器: 开放端口5000（如需外网访问）

#### 4. 获取访问地址
服务启动后，系统会显示：
- 本地访问: `http://localhost:5000`
- 局域网访问: `http://你的IP地址:5000`

---

## 🌐 内网穿透方案（免费）

### 方案一：ngrok（推荐）

#### 1. 下载安装
- 访问 [ngrok.com](https://ngrok.com)
- 注册免费账户
- 下载客户端

#### 2. 配置使用
```bash
# 认证（替换为你的token）
ngrok authtoken YOUR_AUTH_TOKEN

# 启动内网穿透
ngrok http 5000
```

#### 3. 获取公网地址
ngrok会提供类似 `https://abc123.ngrok.io` 的地址

### 方案二：frp（完全免费）

#### 1. 下载frp
- 访问 [GitHub frp releases](https://github.com/fatedier/frp/releases)
- 下载适合你系统的版本

#### 2. 配置客户端
创建 `frpc.ini` 文件：
```ini
[common]
server_addr = 你的服务器IP
server_port = 7000

[web]
type = http
local_port = 5000
custom_domains = 你的域名
```

### 方案三：花生壳（国内）
- 下载花生壳客户端
- 注册账户获取免费域名
- 配置端口映射

---

## ☁️ 云服务器部署

### 免费云服务选项

#### 1. Vercel（推荐前端）
```bash
# 安装Vercel CLI
npm i -g vercel

# 部署前端
cd client
vercel --prod
```

#### 2. Railway（全栈）
- 连接GitHub仓库
- 自动部署
- 免费额度充足

#### 3. Render（全栈）
- 免费PostgreSQL数据库
- 自动SSL证书
- GitHub集成

### 付费云服务（低成本）

#### 1. 腾讯云轻量应用服务器
- 价格：24元/月起
- 1核2G内存
- 适合小型应用

#### 2. 阿里云ECS
- 学生优惠：9.5元/月
- 新用户优惠

---

## 🔧 配置优化

### 1. 修改服务器配置
编辑 `server/.env.production`：
```env
# 修改JWT密钥为你自己的
JWT_SECRET=your_super_secret_key_here

# 端口配置
PORT=5000
HOST=0.0.0.0
```

### 2. 数据库持久化
当前使用文件存储，重启不会丢失数据。
如需更高可靠性，可升级到MySQL/PostgreSQL。

### 3. 文件上传配置
上传文件存储在 `server/uploads/` 目录
建议定期备份此目录。

---

## 🛡️ 安全配置

### 1. 修改默认密码
首次部署后立即修改管理员密码：
- 用户名：admin
- 默认密码：admin123

### 2. 配置HTTPS（推荐）
使用Let's Encrypt免费SSL证书：
```bash
# 安装certbot
sudo apt install certbot

# 获取证书
sudo certbot certonly --standalone -d yourdomain.com
```

### 3. 防火墙配置
```bash
# Ubuntu/Debian
sudo ufw allow 5000
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=5000/tcp
sudo firewall-cmd --reload
```

---

## 📱 移动端适配

系统已完全适配移动端，用户可通过手机浏览器直接访问。

---

## 🔍 监控和维护

### 1. 日志查看
服务器控制台会显示所有操作日志

### 2. 性能监控
```bash
# 查看进程状态
ps aux | grep node

# 查看端口占用
netstat -tulpn | grep 5000
```

### 3. 自动重启
使用PM2进程管理器：
```bash
# 安装PM2
npm install -g pm2

# 启动应用
pm2 start server/sqlite-server.js --name "quality-assessment"

# 设置开机自启
pm2 startup
pm2 save
```

---

## ❓ 常见问题解决

### Q1: 端口被占用
```bash
# 查找占用进程
netstat -ano | findstr :5000
# 结束进程
taskkill /PID 进程ID /F
```

### Q2: 无法外网访问
1. 检查防火墙设置
2. 检查路由器端口转发
3. 确认服务器监听0.0.0.0

### Q3: 上传文件失败
1. 检查uploads目录权限
2. 确认文件大小限制
3. 查看服务器日志

### Q4: 数据丢失
1. 检查data.json文件
2. 确认文件权限
3. 定期备份数据

---

## 📞 技术支持

如遇到问题，请检查：
1. Node.js版本是否符合要求
2. 端口是否被占用
3. 防火墙是否正确配置
4. 网络连接是否稳定

---

**部署完成后，将访问地址分享给用户即可使用！**
