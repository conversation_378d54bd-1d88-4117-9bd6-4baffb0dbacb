@echo off
echo ========================================
echo    学生素质测评系统 - 生产环境启动
echo ========================================

echo 正在检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Node.js，请先安装Node.js
    pause
    exit /b 1
)

echo ✅ Node.js环境检查通过

echo 正在安装依赖...
call npm install
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

echo 正在构建前端...
cd client
call npm run build
if errorlevel 1 (
    echo ❌ 前端构建失败
    cd ..
    pause
    exit /b 1
)
cd ..

echo 正在启动生产服务器...
cd server
copy .env.production .env
call npm start

pause
