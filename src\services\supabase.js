import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false
  }
});

// Database helper functions
export const db = {
  // Users table operations
  users: {
    async getByStudentId(studentId) {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('student_id', studentId)
        .single();
      
      if (error) throw error;
      return data;
    },

    async getById(id) {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', id)
        .single();
      
      if (error) throw error;
      return data;
    },

    async getAll(filters = {}) {
      let query = supabase.from('users').select('*');
      
      if (filters.role) {
        query = query.eq('role', filters.role);
      }
      
      if (filters.class_name) {
        query = query.eq('class_name', filters.class_name);
      }
      
      const { data, error } = await query.order('created_at', { ascending: false });
      
      if (error) throw error;
      return data;
    },

    async create(userData) {
      const { data, error } = await supabase
        .from('users')
        .insert(userData)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },

    async update(id, updates) {
      const { data, error } = await supabase
        .from('users')
        .update(updates)
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },

    async delete(id) {
      const { error } = await supabase
        .from('users')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
    }
  },

  // Certificates table operations
  certificates: {
    async getAll(filters = {}) {
      let query = supabase
        .from('certificates')
        .select(`
          *,
          users!inner(name, student_id, class_name),
          certificate_categories!inner(category_name)
        `);
      
      if (filters.user_id) {
        query = query.eq('user_id', filters.user_id);
      }
      
      if (filters.status) {
        query = query.eq('status', filters.status);
      }
      
      if (filters.category_id) {
        query = query.eq('category_id', filters.category_id);
      }
      
      const { data, error } = await query.order('created_at', { ascending: false });
      
      if (error) throw error;
      return data;
    },

    async getById(id) {
      const { data, error } = await supabase
        .from('certificates')
        .select(`
          *,
          users!inner(name, student_id, class_name),
          certificate_categories!inner(category_name)
        `)
        .eq('id', id)
        .single();
      
      if (error) throw error;
      return data;
    },

    async create(certificateData) {
      const { data, error } = await supabase
        .from('certificates')
        .insert(certificateData)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },

    async update(id, updates) {
      const { data, error } = await supabase
        .from('certificates')
        .update(updates)
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },

    async delete(id) {
      const { error } = await supabase
        .from('certificates')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
    }
  },

  // Certificate categories operations
  categories: {
    async getAll() {
      const { data, error } = await supabase
        .from('certificate_categories')
        .select('*')
        .order('category_name');
      
      if (error) throw error;
      return data;
    },

    async getById(id) {
      const { data, error } = await supabase
        .from('certificate_categories')
        .select('*')
        .eq('id', id)
        .single();
      
      if (error) throw error;
      return data;
    },

    async create(categoryData) {
      const { data, error } = await supabase
        .from('certificate_categories')
        .insert(categoryData)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },

    async update(id, updates) {
      const { data, error } = await supabase
        .from('certificate_categories')
        .update(updates)
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },

    async delete(id) {
      const { error } = await supabase
        .from('certificate_categories')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
    }
  },

  // Assessment activities operations
  activities: {
    async getAll() {
      const { data, error } = await supabase
        .from('assessment_activities')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data;
    },

    async getCurrent() {
      const { data, error } = await supabase
        .from('assessment_activities')
        .select('*')
        .eq('is_active', true)
        .single();
      
      if (error) throw error;
      return data;
    }
  },

  // Operation logs
  logs: {
    async create(logData) {
      const { data, error } = await supabase
        .from('operation_logs')
        .insert(logData)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },

    async getAll(filters = {}) {
      let query = supabase
        .from('operation_logs')
        .select(`
          *,
          users!inner(name, student_id)
        `);
      
      if (filters.user_id) {
        query = query.eq('user_id', filters.user_id);
      }
      
      if (filters.action) {
        query = query.eq('action', filters.action);
      }
      
      const { data, error } = await query.order('created_at', { ascending: false });
      
      if (error) throw error;
      return data;
    }
  }
};

// Storage helper functions
export const storage = {
  certificates: {
    async upload(file, fileName) {
      const { data, error } = await supabase.storage
        .from('certificates')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        });
      
      if (error) throw error;
      return data;
    },

    async getPublicUrl(fileName) {
      const { data } = supabase.storage
        .from('certificates')
        .getPublicUrl(fileName);
      
      return data.publicUrl;
    },

    async delete(fileName) {
      const { error } = await supabase.storage
        .from('certificates')
        .remove([fileName]);
      
      if (error) throw error;
    }
  },

  excel: {
    async upload(file, fileName) {
      const { data, error } = await supabase.storage
        .from('excel-files')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        });
      
      if (error) throw error;
      return data;
    },

    async getPublicUrl(fileName) {
      const { data } = supabase.storage
        .from('excel-files')
        .getPublicUrl(fileName);
      
      return data.publicUrl;
    },

    async delete(fileName) {
      const { error } = await supabase.storage
        .from('excel-files')
        .remove([fileName]);
      
      if (error) throw error;
    }
  }
};

export default supabase;
