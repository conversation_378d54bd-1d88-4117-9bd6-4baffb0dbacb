import { supabase, db, storage } from './supabase';
import toast from 'react-hot-toast';

class ApiService {
  constructor() {
    this.baseURL = '/api';
  }

  // Helper method to get auth headers
  getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };
  }

  // Certificate Management
  certificates = {
    // Get all certificates for current user or all (for teachers)
    async getAll(filters = {}) {
      try {
        return await db.certificates.getAll(filters);
      } catch (error) {
        console.error('Error fetching certificates:', error);
        throw error;
      }
    },

    // Get certificate by ID
    async getById(id) {
      try {
        return await db.certificates.getById(id);
      } catch (error) {
        console.error('Error fetching certificate:', error);
        throw error;
      }
    },

    // Submit new certificate with validation
    async submit(certificateData, file) {
      try {
        // Validate submission limits and duplicates
        await this.validateCertificateSubmission(certificateData);

        let fileUrl = null;

        if (file) {
          // Upload file to Supabase Storage
          const fileName = `${Date.now()}_${file.name}`;
          await storage.certificates.upload(file, fileName);
          fileUrl = storage.certificates.getPublicUrl(fileName);
        }

        const certificate = await db.certificates.create({
          ...certificateData,
          file_url: fileUrl,
          status: 'pending',
          created_at: new Date().toISOString()
        });

        // Log the submission
        await db.logs.create({
          user_id: certificateData.user_id,
          action: '证书提交',
          details: {
            certificate_name: certificateData.certificate_name,
            category_id: certificateData.category_id,
            file_name: file?.name
          },
          target_type: 'certificate',
          target_id: certificate.id
        });

        toast.success('证书提交成功！');
        return certificate;
      } catch (error) {
        console.error('Error submitting certificate:', error);
        toast.error(error.message || '证书提交失败');
        throw error;
      }
    },

    // Validate certificate submission limits and duplicates
    async validateCertificateSubmission(certificateData) {
      const { user_id, category_id, certificate_name, award_date } = certificateData;

      // Check submission time window
      await this.validateSubmissionTimeWindow();

      // Get user's existing certificates
      const existingCerts = await db.certificates.getAll({ user_id });

      // Check for duplicate submissions (same name and date)
      const duplicate = existingCerts.find(cert =>
        cert.certificate_name === certificate_name &&
        cert.award_date === award_date &&
        cert.status !== 'rejected'
      );

      if (duplicate) {
        throw new Error('该证书已经提交过，不能重复提交同一项目！');
      }

      // Get category info to determine submission limits
      const category = await db.categories.getById(category_id);
      const categoryName = category?.name || '';

      // Define submission limits by category
      const submissionLimits = {
        '体育竞赛': 2,
        '文艺活动': 2,
        '科学研究': 2, // 学术论文限制
        '英语六级': 1,
        '学生干部': 1
      };

      // Check submission limits
      if (submissionLimits[categoryName]) {
        const categoryCount = existingCerts.filter(cert =>
          cert.category_id === category_id &&
          cert.status !== 'rejected'
        ).length;

        if (categoryCount >= submissionLimits[categoryName]) {
          throw new Error(`${categoryName}类别最多只能提交${submissionLimits[categoryName]}项证书！`);
        }
      }

      return true;
    },

    // Validate submission time window
    async validateSubmissionTimeWindow() {
      try {
        // Get system settings (in a real app, this would come from database)
        const submissionWindow = {
          enabled: false, // This should be fetched from system settings
          startDate: '',
          endDate: '',
          announcement: ''
        };

        // For now, we'll use localStorage to simulate system settings
        const savedSettings = localStorage.getItem('systemSettings');
        if (savedSettings) {
          const settings = JSON.parse(savedSettings);
          if (settings.submissionWindow) {
            Object.assign(submissionWindow, settings.submissionWindow);
          }
        }

        if (!submissionWindow.enabled) {
          return true; // No time window restriction
        }

        const now = new Date();
        const startDate = new Date(submissionWindow.startDate);
        const endDate = new Date(submissionWindow.endDate);

        if (now < startDate) {
          throw new Error(`证书提交尚未开始，开始时间：${startDate.toLocaleString()}`);
        }

        if (now > endDate) {
          throw new Error(`证书提交已截止，截止时间：${endDate.toLocaleString()}`);
        }

        return true;
      } catch (error) {
        throw error;
      }
    },

    // Submit multiple certificates with validation
    async submitBatch(certificatesData, files) {
      try {
        const results = [];
        const errors = [];

        for (let i = 0; i < certificatesData.length; i++) {
          try {
            const certData = certificatesData[i];
            const file = files[i];

            // Validate each certificate
            await this.validateCertificateSubmission(certData);

            // Submit certificate
            const result = await this.submit(certData, file);
            results.push(result);
          } catch (error) {
            errors.push({
              index: i,
              certificate_name: certificatesData[i].certificate_name,
              error: error.message
            });
          }
        }

        if (errors.length > 0) {
          const errorMsg = errors.map(e => `${e.certificate_name}: ${e.error}`).join('\n');
          toast.error(`部分证书提交失败:\n${errorMsg}`);
        }

        if (results.length > 0) {
          toast.success(`成功提交${results.length}个证书！`);
        }

        return { success: results, errors };
      } catch (error) {
        console.error('Error in batch submission:', error);
        toast.error('批量提交失败');
        throw error;
      }
    },

    // Audit certificate (teacher only)
    async audit(id, auditData) {
      try {
        const certificate = await db.certificates.update(id, {
          status: auditData.status,
          score: auditData.score || 0,
          comments: auditData.comments,
          audited_at: new Date().toISOString(),
          audited_by: auditData.audited_by
        });

        // Log the audit
        await db.logs.create({
          user_id: auditData.audited_by,
          action: '证书审核',
          details: {
            certificate_id: id,
            audit_status: auditData.status,
            score: auditData.score,
            comments: auditData.comments
          },
          target_type: 'certificate',
          target_id: id
        });

        toast.success('审核完成！');
        return certificate;
      } catch (error) {
        console.error('Error auditing certificate:', error);
        toast.error('审核失败');
        throw error;
      }
    },

    // Batch audit certificates (teacher only)
    async auditBatch(certificateIds, auditData) {
      try {
        const results = [];
        const errors = [];

        for (const id of certificateIds) {
          try {
            const result = await this.audit(id, auditData);
            results.push(result);
          } catch (error) {
            errors.push({
              id,
              error: error.message
            });
          }
        }

        if (errors.length > 0) {
          toast.error(`部分证书审核失败，共${errors.length}个`);
        }

        if (results.length > 0) {
          toast.success(`成功审核${results.length}个证书！`);
        }

        return { success: results, errors };
      } catch (error) {
        console.error('Error in batch audit:', error);
        toast.error('批量审核失败');
        throw error;
      }
    },

    // Delete certificate
    async delete(id) {
      try {
        // Get certificate info first to delete file
        const certificate = await db.certificates.getById(id);
        
        // Delete file from storage if exists
        if (certificate.file_url) {
          const fileName = certificate.file_url.split('/').pop();
          await storage.certificates.delete(fileName);
        }

        await db.certificates.delete(id);

        // Log the deletion
        await db.logs.create({
          user_id: certificate.user_id,
          action: '证书删除',
          details: {
            certificate_name: certificate.certificate_name,
            category_id: certificate.category_id
          },
          target_type: 'certificate',
          target_id: id
        });

        toast.success('证书删除成功！');
      } catch (error) {
        console.error('Error deleting certificate:', error);
        toast.error('删除失败');
        throw error;
      }
    }
  };

  // Student Management
  students = {
    // Get all students
    async getAll(filters = {}) {
      try {
        return await db.users.getAll({ ...filters, role: 'student' });
      } catch (error) {
        console.error('Error fetching students:', error);
        throw error;
      }
    },

    // Get student by ID with detailed info
    async getById(id) {
      try {
        const student = await db.users.getById(id);
        
        // Get student's certificates
        const certificates = await db.certificates.getAll({ user_id: id });
        
        // Calculate scores
        const scoreBreakdown = this.calculateScoreBreakdown(certificates);
        
        return {
          ...student,
          certificates,
          score_breakdown: scoreBreakdown,
          total_score: Object.values(scoreBreakdown).reduce((sum, score) => sum + score, 0)
        };
      } catch (error) {
        console.error('Error fetching student details:', error);
        throw error;
      }
    },

    // Create new student
    async create(studentData) {
      try {
        const student = await db.users.create({
          ...studentData,
          role: 'student',
          created_at: new Date().toISOString()
        });

        // Log the creation
        await db.logs.create({
          user_id: studentData.created_by,
          action: '学生创建',
          details: {
            student_name: studentData.name,
            student_id: studentData.student_id,
            class_name: studentData.class_name
          },
          target_type: 'user',
          target_id: student.id
        });

        toast.success('学生创建成功！');
        return student;
      } catch (error) {
        console.error('Error creating student:', error);
        toast.error('创建失败');
        throw error;
      }
    },

    // Update student info
    async update(id, updates) {
      try {
        const student = await db.users.update(id, {
          ...updates,
          updated_at: new Date().toISOString()
        });

        toast.success('学生信息更新成功！');
        return student;
      } catch (error) {
        console.error('Error updating student:', error);
        toast.error('更新失败');
        throw error;
      }
    },

    // Delete student
    async delete(id) {
      try {
        const student = await db.users.getById(id);
        
        // Delete all student's certificates first
        const certificates = await db.certificates.getAll({ user_id: id });
        for (const cert of certificates) {
          await this.certificates.delete(cert.id);
        }

        await db.users.delete(id);

        // Log the deletion
        await db.logs.create({
          user_id: id, // Will be logged as system action
          action: '学生删除',
          details: {
            student_name: student.name,
            student_id: student.student_id
          },
          target_type: 'user',
          target_id: id
        });

        toast.success('学生删除成功！');
      } catch (error) {
        console.error('Error deleting student:', error);
        toast.error('删除失败');
        throw error;
      }
    }
  };

  // Certificate Categories
  categories = {
    async getAll() {
      try {
        return await db.categories.getAll();
      } catch (error) {
        console.error('Error fetching categories:', error);
        throw error;
      }
    },

    async create(categoryData) {
      try {
        const category = await db.categories.create(categoryData);
        toast.success('类别创建成功！');
        return category;
      } catch (error) {
        console.error('Error creating category:', error);
        toast.error('创建失败');
        throw error;
      }
    },

    async update(id, updates) {
      try {
        const category = await db.categories.update(id, updates);
        toast.success('类别更新成功！');
        return category;
      } catch (error) {
        console.error('Error updating category:', error);
        toast.error('更新失败');
        throw error;
      }
    },

    async delete(id) {
      try {
        await db.categories.delete(id);
        toast.success('类别删除成功！');
      } catch (error) {
        console.error('Error deleting category:', error);
        toast.error('删除失败');
        throw error;
      }
    }
  };

  // Calculate comprehensive evaluation score (course grades 80% + quality assessment 20%)
  calculateComprehensiveScore(courseGrades, certificates) {
    // Calculate course score (80% weight)
    const courseScore = this.calculateCourseScore(courseGrades);

    // Calculate quality assessment score (20% weight)
    const qualityScore = this.calculateQualityScore(certificates);

    // Comprehensive score = course score (80%) + quality score (20%)
    const comprehensiveScore = courseScore * 0.8 + qualityScore * 0.2;

    return {
      courseScore: parseFloat(courseScore.toFixed(2)),
      qualityScore: parseFloat(qualityScore.toFixed(2)),
      comprehensiveScore: parseFloat(comprehensiveScore.toFixed(2))
    };
  }

  // Calculate course score using weighted average
  calculateCourseScore(courseGrades) {
    if (!courseGrades || courseGrades.length === 0) return 0;

    const totalCredits = courseGrades.reduce((sum, course) => sum + course.credits, 0);
    const weightedSum = courseGrades.reduce((sum, course) => sum + (course.grade * course.credits), 0);

    return totalCredits > 0 ? weightedSum / totalCredits : 0;
  }

  // Calculate quality assessment score (素质测评成绩)
  calculateQualityScore(certificates) {
    const breakdown = this.calculateScoreBreakdown(certificates);
    return breakdown.total; // Return total quality score (out of 100)
  }

  // Helper method to calculate score breakdown with comprehensive rules
  calculateScoreBreakdown(certificates, baseScoreDeductions = 0) {
    const breakdown = {
      professional: 0,
      sports: 0,
      moral: 0,
      base: Math.max(25.0 - baseScoreDeductions, 0) // Base score with deductions
    };

    // Track submission counts for limits
    const submissionCounts = {
      sports: 0,
      arts: 0,
      papers: 0,
      cet6: 0,
      leadership: 0
    };

    // Separate certificates by category for proper scoring
    const categorizedCerts = {
      professional: [],
      sports: [],
      moral: []
    };

    certificates.forEach(cert => {
      if (cert.status !== 'approved') return;

      const categoryName = cert.category_name || '';

      // Categorize certificates
      if (['学科竞赛', '实践技能类竞赛', '学生创新型项目', '专业认证', '科学研究'].includes(categoryName)) {
        categorizedCerts.professional.push(cert);
      } else if (['体育竞赛', '文艺活动', '英语六级'].includes(categoryName)) {
        categorizedCerts.sports.push(cert);
      } else if (['学生干部', '社会实践志愿服务', '新闻采编写', '见义勇为等表彰'].includes(categoryName)) {
        categorizedCerts.moral.push(cert);
      }
    });

    // Calculate professional activities score (max 30 points)
    categorizedCerts.professional.forEach(cert => {
      const score = this.calculateDetailedCertificateScore(cert);
      breakdown.professional += score;
    });
    breakdown.professional = Math.min(breakdown.professional, 30);

    // Calculate sports & arts activities score (max 20 points)
    categorizedCerts.sports.forEach(cert => {
      const categoryName = cert.category_name;

      // Apply submission limits
      if (categoryName === '体育竞赛' && submissionCounts.sports < 2) {
        submissionCounts.sports++;
        const score = this.calculateDetailedCertificateScore(cert);
        breakdown.sports += score;
      } else if (categoryName === '文艺活动' && submissionCounts.arts < 2) {
        submissionCounts.arts++;
        const score = this.calculateDetailedCertificateScore(cert);
        breakdown.sports += score;
      } else if (categoryName === '英语六级' && submissionCounts.cet6 < 1) {
        submissionCounts.cet6++;
        breakdown.sports += 6; // Fixed 6 points for CET-6
      }
    });
    breakdown.sports = Math.min(breakdown.sports, 20);

    // Calculate moral character activities score (max 25 points)
    categorizedCerts.moral.forEach(cert => {
      const categoryName = cert.category_name;

      if (categoryName === '学生干部' && submissionCounts.leadership < 1) {
        submissionCounts.leadership++;
        const score = this.calculateDetailedCertificateScore(cert);
        breakdown.moral += score;
      } else if (categoryName !== '学生干部') {
        const score = this.calculateDetailedCertificateScore(cert);
        breakdown.moral += score;
      }
    });
    breakdown.moral = Math.min(breakdown.moral, 25);

    const totalScore = breakdown.base + breakdown.professional + breakdown.sports + breakdown.moral;

    return {
      ...breakdown,
      total: Math.max(totalScore, 0), // Ensure non-negative total
      deductions: baseScoreDeductions,
      submissionCounts
    };
  }

  // Calculate base score deductions
  calculateBaseScoreDeductions(attendanceRecord) {
    let deductions = 0;

    if (attendanceRecord) {
      // Late arrivals: 1 point per occurrence
      deductions += (attendanceRecord.late_count || 0) * 1;

      // Absences: 2 points per occurrence
      deductions += (attendanceRecord.absence_count || 0) * 2;

      // COVID-19 violations: 5 points per occurrence
      deductions += (attendanceRecord.covid_violations || 0) * 5;
    }

    return deductions;
  }

  // Calculate detailed certificate score with comprehensive rules
  calculateDetailedCertificateScore(cert) {
    let baseScore = 0;

    // Get category information
    const categoryName = cert.category_name || '';
    const subcategory = cert.subcategory || '';

    // Apply category-specific scoring rules
    if (categoryName === '学生创新型项目') {
      // Innovation projects have role-based scoring
      baseScore = this.calculateInnovationProjectScore(cert);
    } else if (categoryName === '专业认证') {
      // Professional certifications
      baseScore = this.calculateProfessionalCertScore(cert);
    } else if (categoryName === '科学研究') {
      // Research papers and patents
      baseScore = this.calculateResearchScore(cert);
    } else if (categoryName === '英语六级') {
      // CET-6 fixed score
      baseScore = 6;
    } else if (categoryName === '学生干部') {
      // Student leadership roles
      baseScore = this.calculateLeadershipScore(cert);
    } else {
      // General competition scoring
      baseScore = this.calculateCompetitionScore(cert);
    }

    // Apply team size adjustments (only for specific categories)
    const teamCategories = ['实践技能类竞赛', '体育竞赛', '文艺活动'];
    if (teamCategories.includes(categoryName) && cert.team_size) {
      if (cert.team_size >= 2 && cert.team_size <= 3) {
        baseScore = baseScore / 2;
      } else if (cert.team_size > 3) {
        baseScore = baseScore / 3;
      }
    }

    return parseFloat(baseScore.toFixed(2));
  }

  // Calculate innovation project score with role distinction
  calculateInnovationProjectScore(cert) {
    const { level, rank, role } = cert;
    let baseScore = 0;

    // Base scores by level and rank
    const scores = {
      '国家级': { '一等奖': 10, '二等奖': 9, '三等奖': 8 },
      '省级': { '一等奖': 8, '二等奖': 7, '三等奖': 6 },
      '校级': { '一等奖': 6, '二等奖': 5, '三等奖': 4 },
      '院级': { '一等奖': 4, '二等奖': 3, '三等奖': 2 }
    };

    baseScore = scores[level]?.[rank] || 0;

    // Apply role-based adjustment
    if (role === '参与成员' || role === '成员') {
      baseScore = baseScore * 0.5; // Members get 50% of host score
    }
    // Host/主持人 gets full score (no adjustment needed)

    return baseScore;
  }

  // Calculate general competition score
  calculateCompetitionScore(cert) {
    const { level, rank } = cert;

    const scores = {
      '国家级': { '一等奖': 12, '二等奖': 10, '三等奖': 8, '其他': 6 },
      '省级': { '一等奖': 8, '二等奖': 7, '三等奖': 6, '其他': 4 },
      '校级': { '一等奖': 6, '二等奖': 5, '三等奖': 4, '其他': 2 },
      '院级': { '一等奖': 4, '二等奖': 3, '三等奖': 2, '其他': 0 }
    };

    return scores[level]?.[rank] || scores[level]?.['其他'] || 0;
  }

  // Calculate professional certification score
  calculateProfessionalCertScore(cert) {
    const { level } = cert;

    const scores = {
      '高级': 8,
      '中级': 6,
      '初级': 2
    };

    return scores[level] || 0;
  }

  // Calculate research score (papers and patents)
  calculateResearchScore(cert) {
    const { subcategory } = cert;

    if (subcategory === '学术论文') {
      return 6; // Per paper, max 2 papers
    } else if (subcategory === '发明专利') {
      return cert.role === '发明人' ? 15 : 5; // Inventor vs participant
    }

    return 0;
  }

  // Calculate leadership role score
  calculateLeadershipScore(cert) {
    const { level } = cert;

    const scores = {
      '校级以上': 10,
      '院级主席团': 8,
      '部长级': 6,
      '寝室长': 3
    };

    return scores[level] || 0;
  }

  // Announcements methods
  async getAnnouncements() {
    try {
      const { data, error } = await supabase
        .from('announcements')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Get announcements error:', error);
      toast.error('获取公告列表失败');
      throw error;
    }
  }

  async getAnnouncementsForStudent() {
    try {
      const { data, error } = await supabase
        .from('announcements')
        .select('*')
        .in('target_audience', ['all', 'students'])
        .eq('status', 'active')
        .or('expires_at.is.null,expires_at.gt.' + new Date().toISOString())
        .order('priority', { ascending: false })
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Get student announcements error:', error);
      toast.error('获取公告失败');
      throw error;
    }
  }

  async createAnnouncement(announcementData) {
    try {
      const { data, error } = await supabase
        .from('announcements')
        .insert([{
          ...announcementData,
          created_at: new Date().toISOString(),
          view_count: 0
        }])
        .select()
        .single();

      if (error) throw error;
      toast.success('公告发布成功');
      return data;
    } catch (error) {
      console.error('Create announcement error:', error);
      toast.error('发布公告失败');
      throw error;
    }
  }

  async updateAnnouncement(id, announcementData) {
    try {
      const { data, error } = await supabase
        .from('announcements')
        .update({
          ...announcementData,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      toast.success('公告更新成功');
      return data;
    } catch (error) {
      console.error('Update announcement error:', error);
      toast.error('更新公告失败');
      throw error;
    }
  }

  async deleteAnnouncement(id) {
    try {
      const { error } = await supabase
        .from('announcements')
        .delete()
        .eq('id', id);

      if (error) throw error;
      toast.success('公告删除成功');
    } catch (error) {
      console.error('Delete announcement error:', error);
      toast.error('删除公告失败');
      throw error;
    }
  }

  async updateAnnouncementStatus(id, status) {
    try {
      const { data, error } = await supabase
        .from('announcements')
        .update({
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      toast.success(`公告已${status === 'active' ? '启用' : '禁用'}`);
      return data;
    } catch (error) {
      console.error('Update announcement status error:', error);
      toast.error('状态更新失败');
      throw error;
    }
  }

  async markAnnouncementAsRead(id) {
    try {
      // Increment view count
      const { error } = await supabase
        .rpc('increment_announcement_views', { announcement_id: id });

      if (error) throw error;
    } catch (error) {
      console.error('Mark announcement as read error:', error);
      // Don't show error toast for this as it's not critical
    }
  }

  // System logs methods
  async getSystemLogs() {
    try {
      const { data, error } = await supabase
        .from('system_logs')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(1000); // Limit to recent 1000 logs

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Get system logs error:', error);
      toast.error('获取系统日志失败');
      throw error;
    }
  }

  async createSystemLog(logData) {
    try {
      const { data, error } = await supabase
        .from('system_logs')
        .insert([{
          ...logData,
          created_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Create system log error:', error);
      // Don't show error toast for logging as it's background operation
      throw error;
    }
  }

  async logUserAction(action, actionType, description, details = {}) {
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');

      // 只记录管理员的数据修改操作，不记录普通查看操作
      const isDataModification = [
        'create', 'update', 'delete', 'approve', 'reject',
        'audit_certificate', 'create_announcement', 'update_announcement', 'delete_announcement',
        'import_data', 'export_data', 'modify_settings', 'create_user', 'update_user', 'delete_user'
      ].includes(action);

      const isAdmin = user.role === 'admin' || user.role === 'teacher';

      // 只有管理员进行数据修改操作时才记录日志
      if (isAdmin && isDataModification) {
        await this.createSystemLog({
          user_id: user.id || 'unknown',
          user_name: user.name || 'Unknown User',
          user_role: user.role || 'unknown',
          action,
          action_type: actionType,
          description,
          ip_address: 'Unknown', // In real app, get from server
          user_agent: navigator.userAgent,
          status: 'success',
          details
        });
      }
    } catch (error) {
      console.error('Log user action error:', error);
      // Silently fail for logging
    }
  }
}

// Create singleton instance
const apiService = new ApiService();

// Add announcements namespace for backward compatibility
apiService.announcements = {
  getAll: () => apiService.getAnnouncements(),
  getForStudent: () => apiService.getAnnouncementsForStudent(),
  create: (data) => apiService.createAnnouncement(data),
  update: (id, data) => apiService.updateAnnouncement(id, data),
  delete: (id) => apiService.deleteAnnouncement(id),
  updateStatus: (id, status) => apiService.updateAnnouncementStatus(id, status),
  markAsRead: (id) => apiService.markAnnouncementAsRead(id)
};

export default apiService;
