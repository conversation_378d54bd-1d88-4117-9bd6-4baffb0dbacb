# 🚀 快速开始 - 5分钟部署指南

## 📋 三种部署方式

### 🏠 方式一：本地部署（推荐）
**适用场景**：局域网内使用，如学校内部网络

1. **一键部署**
   ```
   双击运行 deploy.bat
   ```

2. **获取访问地址**
   - 本地访问：`http://localhost:5000`
   - 局域网访问：`http://你的电脑IP:5000`

3. **分享给他人**
   - 确保他人与你在同一局域网
   - 分享局域网地址即可

---

### 🌐 方式二：内网穿透（免费）
**适用场景**：任何人都能通过互联网访问

#### 使用ngrok（推荐）

1. **注册ngrok账户**
   - 访问 [ngrok.com](https://ngrok.com)
   - 注册免费账户
   - 获取认证token

2. **下载安装ngrok**
   - 下载适合你系统的版本
   - 解压到任意目录

3. **配置认证**
   ```bash
   ngrok authtoken 你的token
   ```

4. **一键启动**
   ```
   双击运行 start-with-ngrok.bat
   ```

5. **获取公网地址**
   - ngrok会显示类似 `https://abc123.ngrok.io` 的地址
   - 将此地址分享给任何人即可访问

#### 使用其他免费工具
- **花生壳**：国内用户推荐
- **frp**：完全免费，需要自己的服务器
- **localtunnel**：简单易用

---

### ☁️ 方式三：云服务器（付费）
**适用场景**：长期稳定运行，专业部署

#### 免费选项
- **Vercel**：适合前端部署
- **Railway**：全栈部署，有免费额度
- **Render**：免费PostgreSQL数据库

#### 低成本选项
- **腾讯云轻量服务器**：24元/月起
- **阿里云ECS**：学生价9.5元/月

---

## 🔑 默认账户信息

### 管理员账户
- **用户名**：`admin`
- **密码**：`admin123`
- **权限**：完整管理权限

### 学生账户
- **创建方式**：通过Excel导入或手动添加
- **默认密码**：学号
- **权限**：学生功能权限

---

## 📱 使用说明

### 教师操作流程
1. 使用管理员账户登录
2. 导入学生信息（Excel文件）
3. 学生提交证书后进行审核
4. 查看和管理学生成绩
5. 导出最终成绩报表

### 学生操作流程
1. 使用学号和密码登录
2. 提交各类证书材料
3. 查看证书审核状态
4. 查看个人成绩详情

---

## 🛡️ 安全提醒

1. **修改默认密码**
   - 首次部署后立即修改管理员密码

2. **防火墙配置**
   - Windows：允许端口5000通过防火墙
   - 路由器：如需外网访问，开放端口转发

3. **数据备份**
   - 定期备份 `server/data.json` 文件
   - 备份 `server/uploads/` 目录

---

## ❓ 常见问题

### Q: 无法访问系统
**A**: 检查防火墙设置，确保端口5000未被阻止

### Q: 其他人无法访问
**A**: 确保分享的是正确的IP地址，且在同一网络环境

### Q: ngrok连接失败
**A**: 检查网络连接，确认token配置正确

### Q: 上传文件失败
**A**: 检查文件大小（限制50MB），确认格式正确

---

## 📞 获取帮助

如遇到问题：
1. 查看控制台错误信息
2. 检查网络连接状态
3. 确认环境配置正确
4. 参考详细的《部署指南.md》

---

**🎉 部署完成后，将访问地址分享给用户即可开始使用！**
