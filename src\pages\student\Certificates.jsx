import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import apiService from '../../services/api';
import toast from 'react-hot-toast';

// 学科竞赛具体选项
const SUBJECT_COMPETITIONS = [
  '中国(国际)"互联网+"大学生创新创业大赛',
  '"挑战杯"中国大学生创业计划大赛',
  '"挑战杯"全国大学生课外学术科技作品竞赛',
  '全国大学生创新创业训练计划年会展示',
  '中国大学生服务外包创新创业大赛',
  '中国高校计算机大赛-大数据挑战赛、团体程序设计天梯赛、移动应用创新赛、网络技术挑战赛、人工智能创意赛、微信小程序应用开发赛',
  '全国大学生信息安全大赛',
  '中国机器人大赛暨RoboCup机器人世界杯中国赛',
  'ACM-ICPC国际大学生程序设计竞赛',
  '中国大学生计算机设计大赛',
  '蓝桥杯全国软件和信息技术专业人才大赛',
  '"中国软件杯"大学生软件设计大赛',
  '全国大学生数学建模竞赛',
  '"新华三杯"全国大学生数字技术大赛',
  '中国机器人及人工智能大赛',
  'RoboCom机器人开发者大赛',
  '华为ICT大赛',
  '中国高校智能机器人创意大赛',
  '中国大学生程序设计竞赛',
  '河南省"新工科"大学生工业设计大赛',
  '河南省高等学校信息安全对抗大赛',
  '黄河鲲鹏开发者大赛',
  '河南大学计算机程序设计类大赛',
  '百度之星·程序设计大赛',
  '全国大学生计算机系统能力大赛',
  '全国大学生物联网设计竞赛',
  '全国大学生信息安全与对抗技术竞赛',
  '全球校园人工智能算法精英大赛',
  '中国工程机器人大赛暨国际公开赛',
  '国际大学生智能农业装备创新大赛'
];

// 文艺活动具体选项
const CULTURAL_ACTIVITIES = [
  '全国大学生节能减排社会实践与科技竞赛',
  '"外研社·国才杯"全国大学生英语系列赛-英语演讲、英语辩论、英语写作、英语阅读',
  '全国大学生电子设计竞赛',
  '全国大学生智能汽车竞赛',
  '全国大学生机器人大赛-RoboMaster、RoboCon',
  '中国大学生机械工程创新创意大赛-过程装备实践与创新赛、铸造工艺设计赛、材料热处理创新创意赛、起重机创意赛',
  '"西门子杯"中国智能制造挑战赛',
  '全国大学生集成电路创新创业大赛',
  '全国大学生机械创新设计大赛',
  '全国大学生金相技能大赛',
  '全国大学生光电设计大赛',
  '全国大学生交通科技大赛',
  '全国大学生工程训练综合能力竞赛',
  '全国大学生先进成图技术与产品信息建模创新大赛',
  '全国周培源大学生力学竞赛',
  '全国大学生结构设计竞赛',
  '全国大学生化学实验邀请赛',
  '全国大学生化工设计竞赛',
  '全国高等医学院校大学生临床技能竞赛',
  '全国大学生电子商务"创新、创意及创业"挑战赛',
  '全国大学生物流设计大赛',
  '全国大学生市场调查与分析大赛',
  '全国大学生地质技能竞赛',
  '全国大学生广告艺术大赛',
  '全国高校数字艺术设计大赛',
  '两岸新锐设计竞赛"华灿奖"',
  '米兰设计周--中国高校设计学科师生优秀作品展',
  '中美青年创客大赛',
  '中国青年志愿服务项目大赛',
  '中国创新创业大赛',
  '"中国创翼"创业创新大赛',
  '"创客中国"中小企业创新创业大赛',
  '全国移动互联创新大赛',
  '全国"互联网+"快递大学生创新创业大赛',
  '全国大学生生命科学创新创业大赛',
  '全国大学生生命科学竞赛',
  '丘成桐大学生数学竞赛',
  '全国大学生数学竞赛',
  '美国大学生数学建模竞赛',
  '全国大学生物理实验竞赛',
  '全国高校无机非金属材料基础知识大赛',
  '"纳米之星"创新创业大赛总决赛',
  '中国大学生医学技术技能大赛',
  '全国护理专业本科临床技能大赛',
  '全国大学生药苑论坛',
  '全国中医药高等教育技能大赛——中药学类专业学生知识技能大赛',
  '全国大学生化工实验大赛',
  '中国高校地理科学展示大赛',
  '全国大学生GIS应用技能大赛',
  '全国大学生土地利用规划技能大赛',
  '全国大学生自然资源科技作品大赛',
  '全国高校地理师范生教学技能大赛',
  '"共享杯"大学生科技资源共享服务创新大赛',
  '全国大学生结构设计信息技术大赛',
  '全国高校BIM毕业设计大赛',
  '清华大学中国公共政策案例分析大赛',
  '"河仁杯"全国大学生社会调查技能大赛',
  '"淮海杯"全国高校模拟法庭大赛',
  '国际刑事法院模拟法庭(英文)',
  '"理律杯"全国高校模拟法庭竞赛',
  '杰赛普国际法模拟法庭大赛',
  '"iTeach"全国大学生数字化教育应用创新大赛',
  '全国高校学前(幼儿)教育专业优秀毕业论文奖',
  '全国大学生统计建模大赛',
  '全国高等师范院校历史学专业本科生教学技能比赛',
  '"发现中国"李济考古学奖学金',
  '全国大学生口述史成果交流赛',
  '全国大学生红色旅游创意策划大赛',
  'NextIdea腾讯新文创大赛',
  '全国大学生世界遗产保护与可持续利用提案大赛',
  'HRU大学生人力资源职业技能大赛',
  '全国大学生人力资源管理知识技能竞赛',
  '全国大学生物流仿真设计大赛',
  '中华全国日语演讲比赛',
  '中国日报社"21世纪杯"全国英语演讲比赛',
  '全国口译大赛',
  '全国高校俄语大赛',
  '全国大学生英语竞赛',
  '中国大学生广告艺术节学院奖',
  '全国大学生网络编辑创新大赛',
  '全国普通高等学校音乐学(教师教育)本科专业学生基本功大赛',
  '"为中国而设计"全国环境艺术设计大展',
  '全国大学生艺术展演活动',
  '中国大学生武术锦标赛',
  '国际武术大赛及武术节',
  '全国大学生运动会-各单项奖',
  '全国大学生联赛-各类联赛',
  '全国大学生锦标赛-各单项奖',
  '河南省高等学校师范教育专业毕业生教学技能比赛',
  '河南省大学生职业生涯规划大赛',
  '河南省大学生物理实验竞赛',
  '河南省大学生建筑模型大赛',
  '中南地区高校土木工程专业结构力学竞赛',
  '河南省翻译竞赛',
  '"漾翅杯"法律实践能力大赛',
  '"卓越杯"法治辩论赛',
  '河南省高校社会工作案例大赛',
  '"求是杯"全国公共管理案例大赛',
  '中国数据新闻大赛',
  '河南省大学生武术锦标赛',
  '北京电影学院动画学院奖',
  '河南高校摄影大赛',
  '河南省"新人新作"摄影展',
  '新光奖·中国西安国际原创动漫大赛',
  '河南大学辩论赛',
  '河南大学"我身边的创新创业典型"选拔赛',
  '河南大学"就业之星"求职应聘风采大赛',
  '"品时事、鉴青春"时政评论大赛',
  '"信仰之旅"活动大赛',
  '"阅读经典•\'典\'亮人生"征文大赛',
  '大学生学"习"论坛',
  '河南大学"我和我的祖国"朗诵大赛',
  '河南大学弘扬社会主义核心价值观短剧大赛',
  '河南大学英语文化节系列竞赛',
  '河南大学口腔技能竞赛',
  '河南大学大学生物理实验竞赛',
  '河南大学电子组装与设计大赛',
  '河南大学基础医学模型大赛',
  '河南大学健康素养大赛',
  '河南大学人体解剖学知识竞赛',
  '河南大学旅游路线设计大赛',
  '"书香•追梦——跟着习总书记学读书"系列活动',
  'iCAN大学生创新创业大赛',
  '"工行杯"全国大学生金融科技创新大赛',
  '"外教社杯"全国高校学生跨文化能力大赛',
  '全国大学生工业设计大赛',
  '全国大学生水利创新设计大赛',
  '全国大学生化学实验创新设计大赛',
  '全国大学生花园设计建造竞赛',
  '全国大学生测绘学科创新创业智能大赛',
  '全国大学生能源经济学术创意大赛',
  '全国大学生基础医学创新研究暨实验设计论坛（大赛）',
  '全国大学生数字媒体科技作品及创意竞赛',
  '全国本科院校税收风险管控案例大赛',
  '全国企业竞争模拟大赛',
  '全国高等院校数智化企业经营沙盘大赛',
  '全国数字建筑创新应用大赛',
  '"科云杯"全国大学生财会职业能力大赛',
  '"尖烽时刻"酒店管理模拟大赛',
  '全国大学生交通运输科技大赛',
  'UIA霍普杯国际大学生建筑设计竞赛',
  '全国师范院校师范生教学技能竞赛',
  '中国数据新闻大赛',
  '"法理争鸣"高校版权辩论赛',
  '河南省汉字大赛',
  '全国高校商业精英挑战赛——品牌策划竞赛、会展专业创新创业实践竞赛、国际贸易竞赛、创新创业竞赛',
  '中国好创意暨全国数字艺术设计大赛',
  '全国三维数字化创新设计大赛',
  '"学创杯"全国大学生创业综合模拟大赛',
  '"大唐杯"全国大学生移动通信5G技术大赛',
  '全国大学生嵌入式芯片与系统设计竞赛',
  '全国高校体育教育专业学生基本功大赛',
  '中国大学生体育舞蹈锦标赛（区赛、总决赛）',
  '全国大学生混凝土材料设计大赛',
  '全国高等院校建筑与环境设计专业学生美术作品大奖赛',
  '齐越朗诵艺术节暨全国大学生朗诵大会',
  '中华经典诵写讲大赛',
  '大学生科技文化艺术节',
  '中国合唱节',
  '全国高校德语专业大学生德语辩论赛',
  '"儒易杯"中华文化国际翻译大赛',
  '河南省学生体育舞蹈锦标赛',
  '紫金奖——建筑及环境设计大赛',
  '黄河戏剧奖',
  '河南省合唱节',
  '铁塔记者文化节（新闻采写大赛）',
  '自定义输入'
];

const Certificates = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [certificates, setCertificates] = useState([]);
  const [categories, setCategories] = useState([]);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploadForm, setUploadForm] = useState({
    category_id: '',
    certificate_name: '',
    level: '',
    award_date: '',
    team_size: '',
    role: '',
    file: null
  });

  // Get selected category info for dynamic form fields
  const selectedCategory = categories.find(cat => cat.id === parseInt(uploadForm.category_id));
  const categoryName = selectedCategory?.category_name || '';

  // Determine which fields to show based on category
  const showTeamFields = ['实践技能类竞赛', '体育竞赛', '文艺活动'].includes(categoryName);
  const showRoleField = categoryName === '学生创新型项目';
  const showLevelField = !['英语六级'].includes(categoryName);
  const [uploading, setUploading] = useState(false);
  const [submissionWindow, setSubmissionWindow] = useState({
    enabled: false,
    startDate: '',
    endDate: '',
    announcement: '',
    isOpen: true
  });

  useEffect(() => {
    fetchData();
    checkSubmissionWindow();
  }, []);

  const checkSubmissionWindow = () => {
    try {
      const savedSettings = localStorage.getItem('systemSettings');
      if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        if (settings.submissionWindow) {
          const window = settings.submissionWindow;
          const now = new Date();
          const startDate = new Date(window.startDate);
          const endDate = new Date(window.endDate);

          setSubmissionWindow({
            ...window,
            isOpen: !window.enabled || (now >= startDate && now <= endDate)
          });
        }
      }
    } catch (error) {
      console.error('Error checking submission window:', error);
    }
  };

  const fetchData = async () => {
    try {
      setLoading(true);

      // Fetch certificates and categories using API service
      const [certificates, categories] = await Promise.all([
        apiService.certificates.getAll({ user_id: user.id }),
        apiService.categories.getAll()
      ]);

      setCertificates(certificates);
      setCategories(categories);
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('获取数据失败');

      // Fallback to mock data
      setCertificates([
        {
          id: 1,
          certificate_name: '全国大学生数学建模竞赛',
          category_name: '学科竞赛',
          level: '省级一等奖',
          score: 8,
          status: 'approved',
          award_date: '2024-10-15',
          created_at: '2024-10-20'
        },
        {
          id: 2,
          certificate_name: '体育竞赛',
          category_name: '体育竞赛',
          level: '校级第一名',
          score: 5,
          status: 'pending',
          award_date: '2024-11-01',
          created_at: '2024-11-05'
        }
      ]);

      setCategories([
        { id: 1, category_name: '学科竞赛' },
        { id: 2, category_name: '体育竞赛' },
        { id: 3, category_name: '文艺活动' },
        { id: 4, category_name: '专业认证' },
        { id: 5, category_name: '科学研究' }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleUploadSubmit = async (e) => {
    e.preventDefault();

    if (!uploadForm.category_id || !uploadForm.certificate_name || !uploadForm.level || !uploadForm.file) {
      toast.error('请填写所有必填字段');
      return;
    }

    setUploading(true);
    try {
      // Prepare certificate data
      const certificateData = {
        user_id: user.id,
        category_id: parseInt(uploadForm.category_id),
        certificate_name: uploadForm.certificate_name,
        level: uploadForm.level,
        award_date: uploadForm.award_date,
        team_size: uploadForm.team_size ? parseInt(uploadForm.team_size) : null,
        role: uploadForm.role || null
      };

      // Submit certificate using API service
      await apiService.certificates.submit(certificateData, uploadForm.file);

      // Reset form and close modal
      setShowUploadModal(false);
      setUploadForm({
        category_id: '',
        certificate_name: '',
        level: '',
        award_date: '',
        team_size: '',
        role: '',
        file: null
      });

      // Refresh the list
      fetchData();
    } catch (error) {
      console.error('Upload error:', error);
      // Error handling is done in the API service
    } finally {
      setUploading(false);
    }
  };

  const getStatusBadge = (status) => {
    const badges = {
      pending: 'bg-yellow-100 text-yellow-800',
      approved: 'bg-green-100 text-green-800',
      rejected: 'bg-red-100 text-red-800'
    };
    
    const labels = {
      pending: '待审核',
      approved: '已通过',
      rejected: '已拒绝'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${badges[status] || badges.pending}`}>
        {labels[status] || '未知'}
      </span>
    );
  };

  if (loading) {
    return <LoadingSpinner text="加载证书列表中..." />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">证书管理</h1>
        <button
          onClick={() => {
            if (submissionWindow.enabled && !submissionWindow.isOpen) {
              toast.error('当前不在证书提交时间窗口内，无法提交证书');
              return;
            }
            setShowUploadModal(true);
          }}
          disabled={submissionWindow.enabled && !submissionWindow.isOpen}
          className={`px-4 py-2 rounded-lg transition-colors ${
            submissionWindow.enabled && !submissionWindow.isOpen
              ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          + 提交证书
        </button>
      </div>

      {/* Submission Window Notice */}
      {submissionWindow.enabled && (
        <div className={`p-4 rounded-lg ${submissionWindow.isOpen ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <span className="text-2xl">{submissionWindow.isOpen ? '✅' : '⏰'}</span>
            </div>
            <div className="ml-3">
              <h3 className={`text-sm font-medium ${submissionWindow.isOpen ? 'text-green-800' : 'text-red-800'}`}>
                {submissionWindow.isOpen ? '证书提交窗口已开放' : '证书提交窗口已关闭'}
              </h3>
              <div className={`mt-2 text-sm ${submissionWindow.isOpen ? 'text-green-700' : 'text-red-700'}`}>
                <p>提交时间：{new Date(submissionWindow.startDate).toLocaleString()} - {new Date(submissionWindow.endDate).toLocaleString()}</p>
                {submissionWindow.announcement && (
                  <p className="mt-1">{submissionWindow.announcement}</p>
                )}
                {!submissionWindow.isOpen && (
                  <p className="mt-1 font-medium">当前无法提交证书，请在规定时间内提交。</p>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Statistics */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-3">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">📜</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">总证书数</dt>
                  <dd className="text-lg font-medium text-gray-900">{certificates.length}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">⏳</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">待审核</dt>
                  <dd className="text-lg font-medium text-yellow-600">
                    {certificates.filter(cert => cert.status === 'pending').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">✅</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">已通过</dt>
                  <dd className="text-lg font-medium text-green-600">
                    {certificates.filter(cert => cert.status === 'approved').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Certificates List */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <ul className="divide-y divide-gray-200">
          {certificates.length === 0 ? (
            <li className="px-6 py-8 text-center text-gray-500">
              <span className="text-4xl mb-4 block">📜</span>
              <p>暂无证书记录</p>
              <p className="text-sm mt-2">点击上方"提交证书"按钮开始添加</p>
            </li>
          ) : (
            certificates.map((cert) => (
              <li key={cert.id}>
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <span className="text-2xl">🏆</span>
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-900">
                          {cert.certificate_name}
                        </p>
                        <p className="text-sm text-gray-500">
                          {cert.category_name} · {cert.level}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-900">
                          {cert.score}分
                        </p>
                        <p className="text-sm text-gray-500">
                          {new Date(cert.award_date).toLocaleDateString()}
                        </p>
                      </div>
                      {getStatusBadge(cert.status)}
                    </div>
                  </div>
                </div>
              </li>
            ))
          )}
        </ul>
      </div>

      {/* Upload Modal */}
      {showUploadModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">提交证书</h3>
              <form onSubmit={handleUploadSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">证书类别 *</label>
                  <select
                    value={uploadForm.category_id}
                    onChange={(e) => setUploadForm({...uploadForm, category_id: e.target.value})}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  >
                    <option value="">请选择类别</option>
                    {categories.map(cat => (
                      <option key={cat.id} value={cat.id}>{cat.category_name}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">证书名称 *</label>
                  {categoryName === '学科竞赛' ? (
                    <select
                      value={uploadForm.certificate_name}
                      onChange={(e) => setUploadForm({...uploadForm, certificate_name: e.target.value})}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required
                    >
                      <option value="">请选择学科竞赛</option>
                      {SUBJECT_COMPETITIONS.map((competition, index) => (
                        <option key={index} value={competition}>{competition}</option>
                      ))}
                    </select>
                  ) : categoryName === '文艺活动' ? (
                    <div>
                      <select
                        value={uploadForm.certificate_name === '自定义输入' ? '自定义输入' : uploadForm.certificate_name}
                        onChange={(e) => {
                          if (e.target.value === '自定义输入') {
                            setUploadForm({...uploadForm, certificate_name: '', customInput: true});
                          } else {
                            setUploadForm({...uploadForm, certificate_name: e.target.value, customInput: false});
                          }
                        }}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        required
                      >
                        <option value="">请选择文艺活动</option>
                        {CULTURAL_ACTIVITIES.map((activity, index) => (
                          <option key={index} value={activity}>{activity}</option>
                        ))}
                      </select>
                      {uploadForm.customInput && (
                        <div className="mt-2">
                          <input
                            type="text"
                            value={uploadForm.certificate_name}
                            onChange={(e) => setUploadForm({...uploadForm, certificate_name: e.target.value})}
                            className="block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            placeholder="请输入自定义文艺活动名称"
                            required
                          />
                          <p className="mt-1 text-sm text-orange-600">
                            ⚠️ 自定义输入的活动需要特殊审核标记
                          </p>
                        </div>
                      )}
                    </div>
                  ) : (
                    <input
                      type="text"
                      value={uploadForm.certificate_name}
                      onChange={(e) => setUploadForm({...uploadForm, certificate_name: e.target.value})}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="请输入证书名称"
                      required
                    />
                  )}
                </div>

                {showLevelField && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">获奖等级 *</label>
                    <input
                      type="text"
                      value={uploadForm.level}
                      onChange={(e) => setUploadForm({...uploadForm, level: e.target.value})}
                      placeholder="如：省级一等奖、校级第一名"
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-700">获奖日期</label>
                  <input
                    type="date"
                    value={uploadForm.award_date}
                    onChange={(e) => setUploadForm({...uploadForm, award_date: e.target.value})}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* Team Information Fields */}
                {showTeamFields && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">团队人数</label>
                    <select
                      value={uploadForm.team_size}
                      onChange={(e) => setUploadForm({...uploadForm, team_size: e.target.value})}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">个人项目</option>
                      <option value="2">2人团队</option>
                      <option value="3">3人团队</option>
                      <option value="4">4人团队</option>
                      <option value="5">5人团队</option>
                      <option value="6">6人以上团队</option>
                    </select>
                    <p className="text-xs text-gray-500 mt-1">
                      团队项目分数将根据人数调整：2-3人÷2，4人以上÷3
                    </p>
                  </div>
                )}

                {/* Role Information Field */}
                {showRoleField && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">项目角色 *</label>
                    <select
                      value={uploadForm.role}
                      onChange={(e) => setUploadForm({...uploadForm, role: e.target.value})}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required
                    >
                      <option value="">请选择角色</option>
                      <option value="主持人">主持人</option>
                      <option value="参与成员">参与成员</option>
                    </select>
                    <p className="text-xs text-gray-500 mt-1">
                      主持人获得完整分数，参与成员获得50%分数
                    </p>
                  </div>
                )}

                {/* Category-specific hints */}
                {categoryName && (
                  <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <span className="text-blue-400">💡</span>
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-blue-800">提交提示</h3>
                        <div className="mt-2 text-sm text-blue-700">
                          {categoryName === '体育竞赛' && <p>• 每人限报2项，满分10分</p>}
                          {categoryName === '文艺活动' && <p>• 每人限报2项，满分10分</p>}
                          {categoryName === '科学研究' && <p>• 学术论文每人限报2篇</p>}
                          {categoryName === '英语六级' && <p>• 通过即可加6分，只能提交一次</p>}
                          {categoryName === '学生干部' && <p>• 按最高职务加分，不累计</p>}
                          {categoryName === '学生创新型项目' && <p>• 需要区分主持人和参与成员角色</p>}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-700">证书文件 *</label>
                  <input
                    type="file"
                    onChange={(e) => setUploadForm({...uploadForm, file: e.target.files[0]})}
                    accept="image/*,.pdf"
                    className="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">支持图片和PDF格式，最大10MB</p>
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowUploadModal(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                    disabled={uploading}
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={uploading}
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"
                  >
                    {uploading ? '提交中...' : '提交'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Certificates;
