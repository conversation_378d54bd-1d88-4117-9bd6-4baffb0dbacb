import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import apiService from '../../services/api';
import toast from 'react-hot-toast';

const Announcements = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [announcements, setAnnouncements] = useState([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingAnnouncement, setEditingAnnouncement] = useState(null);
  const [addForm, setAddForm] = useState({
    title: '',
    content: '',
    type: 'general',
    priority: 'normal',
    target_audience: 'all',
    expires_at: ''
  });
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    fetchAnnouncements();
  }, []);

  const fetchAnnouncements = async () => {
    try {
      setLoading(true);
      
      // Get announcements using API service
      const announcements = await apiService.announcements.getAll();
      setAnnouncements(announcements);
    } catch (error) {
      console.error('Error fetching announcements:', error);
      toast.error('获取公告列表失败');

      // Fallback to mock data
      setAnnouncements([
        {
          id: 1,
          title: '证书提交截止时间提醒',
          content: '请各位同学注意，本学期证书提交将于2024年12月31日截止，请及时提交相关材料。',
          type: 'important',
          priority: 'high',
          target_audience: 'students',
          status: 'active',
          created_at: '2024-07-01T10:00:00Z',
          expires_at: '2024-12-31T23:59:59Z',
          author_name: '系统管理员',
          view_count: 156
        },
        {
          id: 2,
          title: '系统维护通知',
          content: '系统将于本周六晚上22:00-24:00进行维护升级，期间可能无法正常访问，请提前安排好相关工作。',
          type: 'system',
          priority: 'normal',
          target_audience: 'all',
          status: 'active',
          created_at: '2024-07-05T14:30:00Z',
          expires_at: '2024-07-08T00:00:00Z',
          author_name: '技术部',
          view_count: 89
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleAddSubmit = async (e) => {
    e.preventDefault();

    if (!addForm.title || !addForm.content) {
      toast.error('请填写标题和内容');
      return;
    }

    setSaving(true);
    try {
      const announcementData = {
        ...addForm,
        author_id: user.id,
        status: 'active'
      };

      if (editingAnnouncement) {
        // Update existing announcement
        await apiService.announcements.update(editingAnnouncement.id, announcementData);
        toast.success('公告更新成功');
      } else {
        // Create new announcement
        await apiService.announcements.create(announcementData);
        toast.success('公告发布成功');
      }

      // Reset form and close modal
      setShowAddModal(false);
      setEditingAnnouncement(null);
      setAddForm({
        title: '',
        content: '',
        type: 'general',
        priority: 'normal',
        target_audience: 'all',
        expires_at: ''
      });

      // Refresh the list
      fetchAnnouncements();
    } catch (error) {
      console.error('Save announcement error:', error);
      // Error handling is done in the API service
    } finally {
      setSaving(false);
    }
  };

  const handleEdit = (announcement) => {
    setEditingAnnouncement(announcement);
    setAddForm({
      title: announcement.title,
      content: announcement.content,
      type: announcement.type,
      priority: announcement.priority,
      target_audience: announcement.target_audience,
      expires_at: announcement.expires_at ? announcement.expires_at.split('T')[0] : ''
    });
    setShowAddModal(true);
  };

  const handleDelete = async (id) => {
    if (!window.confirm('确定要删除这个公告吗？')) {
      return;
    }

    try {
      await apiService.announcements.delete(id);
      toast.success('公告删除成功');
      fetchAnnouncements();
    } catch (error) {
      console.error('Delete announcement error:', error);
      toast.error('删除失败');
    }
  };

  const handleToggleStatus = async (id, currentStatus) => {
    try {
      const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
      await apiService.announcements.updateStatus(id, newStatus);
      toast.success(`公告已${newStatus === 'active' ? '启用' : '禁用'}`);
      fetchAnnouncements();
    } catch (error) {
      console.error('Toggle status error:', error);
      toast.error('状态更新失败');
    }
  };

  const getTypeLabel = (type) => {
    const types = {
      general: '一般通知',
      important: '重要通知',
      system: '系统通知',
      urgent: '紧急通知'
    };
    return types[type] || type;
  };

  const getPriorityBadge = (priority) => {
    const badges = {
      low: 'bg-gray-100 text-gray-800',
      normal: 'bg-blue-100 text-blue-800',
      high: 'bg-orange-100 text-orange-800',
      urgent: 'bg-red-100 text-red-800'
    };
    const labels = {
      low: '低',
      normal: '普通',
      high: '高',
      urgent: '紧急'
    };
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${badges[priority]}`}>
        {labels[priority]}
      </span>
    );
  };

  const getStatusBadge = (status) => {
    const badges = {
      active: 'bg-green-100 text-green-800',
      inactive: 'bg-gray-100 text-gray-800'
    };
    const labels = {
      active: '已发布',
      inactive: '已禁用'
    };
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${badges[status]}`}>
        {labels[status]}
      </span>
    );
  };

  if (loading) {
    return <LoadingSpinner text="加载公告列表中..." />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">公告管理</h1>
        <button
          onClick={() => {
            setEditingAnnouncement(null);
            setAddForm({
              title: '',
              content: '',
              type: 'general',
              priority: 'normal',
              target_audience: 'all',
              expires_at: ''
            });
            setShowAddModal(true);
          }}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          + 发布公告
        </button>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-4">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">📢</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">总公告数</dt>
                  <dd className="text-lg font-medium text-gray-900">{announcements.length}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">✅</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">已发布</dt>
                  <dd className="text-lg font-medium text-green-600">
                    {announcements.filter(ann => ann.status === 'active').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">🔥</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">重要通知</dt>
                  <dd className="text-lg font-medium text-orange-600">
                    {announcements.filter(ann => ann.priority === 'high' || ann.priority === 'urgent').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">👀</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">总浏览量</dt>
                  <dd className="text-lg font-medium text-blue-600">
                    {announcements.reduce((sum, ann) => sum + (ann.view_count || 0), 0)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Announcements List */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">公告列表</h3>
          
          {announcements.length === 0 ? (
            <div className="text-center py-8">
              <span className="text-4xl mb-4 block">📢</span>
              <p className="text-gray-500">暂无公告</p>
            </div>
          ) : (
            <div className="space-y-4">
              {announcements.map(announcement => (
                <div key={announcement.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h4 className="text-lg font-medium text-gray-900">{announcement.title}</h4>
                        {getPriorityBadge(announcement.priority)}
                        {getStatusBadge(announcement.status)}
                      </div>
                      
                      <p className="text-gray-600 mb-3 line-clamp-2">{announcement.content}</p>
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>类型：{getTypeLabel(announcement.type)}</span>
                        <span>目标：{announcement.target_audience === 'all' ? '全部用户' : announcement.target_audience === 'students' ? '学生' : '教师'}</span>
                        <span>发布时间：{new Date(announcement.created_at).toLocaleString()}</span>
                        {announcement.expires_at && (
                          <span>过期时间：{new Date(announcement.expires_at).toLocaleString()}</span>
                        )}
                        <span>浏览：{announcement.view_count || 0}次</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2 ml-4">
                      <button
                        onClick={() => handleEdit(announcement)}
                        className="text-blue-600 hover:text-blue-800 text-sm"
                      >
                        编辑
                      </button>
                      <button
                        onClick={() => handleToggleStatus(announcement.id, announcement.status)}
                        className={`text-sm ${announcement.status === 'active' ? 'text-gray-600 hover:text-gray-800' : 'text-green-600 hover:text-green-800'}`}
                      >
                        {announcement.status === 'active' ? '禁用' : '启用'}
                      </button>
                      <button
                        onClick={() => handleDelete(announcement.id)}
                        className="text-red-600 hover:text-red-800 text-sm"
                      >
                        删除
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Add/Edit Announcement Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {editingAnnouncement ? '编辑公告' : '发布公告'}
              </h3>
              <form onSubmit={handleAddSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">标题 *</label>
                  <input
                    type="text"
                    value={addForm.title}
                    onChange={(e) => setAddForm({...addForm, title: e.target.value})}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">内容 *</label>
                  <textarea
                    value={addForm.content}
                    onChange={(e) => setAddForm({...addForm, content: e.target.value})}
                    rows={6}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">类型</label>
                    <select
                      value={addForm.type}
                      onChange={(e) => setAddForm({...addForm, type: e.target.value})}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="general">一般通知</option>
                      <option value="important">重要通知</option>
                      <option value="system">系统通知</option>
                      <option value="urgent">紧急通知</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">优先级</label>
                    <select
                      value={addForm.priority}
                      onChange={(e) => setAddForm({...addForm, priority: e.target.value})}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="low">低</option>
                      <option value="normal">普通</option>
                      <option value="high">高</option>
                      <option value="urgent">紧急</option>
                    </select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">目标用户</label>
                    <select
                      value={addForm.target_audience}
                      onChange={(e) => setAddForm({...addForm, target_audience: e.target.value})}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="all">全部用户</option>
                      <option value="students">学生</option>
                      <option value="teachers">教师</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">过期时间</label>
                    <input
                      type="date"
                      value={addForm.expires_at}
                      onChange={(e) => setAddForm({...addForm, expires_at: e.target.value})}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => {
                      setShowAddModal(false);
                      setEditingAnnouncement(null);
                    }}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={saving}
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"
                  >
                    {saving ? '保存中...' : (editingAnnouncement ? '更新' : '发布')}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Announcements;
