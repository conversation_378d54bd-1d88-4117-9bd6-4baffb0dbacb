import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import apiService from '../../services/api';
import toast from 'react-hot-toast';
import * as XLSX from 'xlsx';

const StudentManagement = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [students, setStudents] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [addForm, setAddForm] = useState({
    student_id: '',
    name: '',
    class_name: '',
    phone: '',
    email: ''
  });
  const [adding, setAdding] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);
  const [importing, setImporting] = useState(false);
  const [importResults, setImportResults] = useState(null);

  useEffect(() => {
    fetchStudents();
  }, []);

  const fetchStudents = async () => {
    try {
      setLoading(true);
      
      // Get students using API service
      const students = await apiService.students.getAll();
      setStudents(students);
    } catch (error) {
      console.error('Error fetching students:', error);
      toast.error('获取学生列表失败');

      // Fallback to mock data
      setStudents([
        {
          id: 1,
          student_id: '2021001',
          name: '张三',
          class_name: '体育教育21-1班',
          phone: '13800138001',
          email: '<EMAIL>',
          total_score: 85.5,
          certificate_count: 5,
          status: 'active',
          created_at: '2021-09-01T00:00:00Z',
          last_login: '2024-01-07T10:30:00Z'
        },
        {
          id: 2,
          student_id: '2021002',
          name: '李四',
          class_name: '体育教育21-1班',
          phone: '13800138002',
          email: '<EMAIL>',
          total_score: 78.0,
          certificate_count: 3,
          status: 'active',
          created_at: '2021-09-01T00:00:00Z',
          last_login: '2024-01-06T15:20:00Z'
        },
        {
          id: 3,
          student_id: '2021003',
          name: '王五',
          class_name: '体育教育21-2班',
          phone: '13800138003',
          email: '<EMAIL>',
          total_score: 92.3,
          certificate_count: 8,
          status: 'active',
          created_at: '2021-09-01T00:00:00Z',
          last_login: '2024-01-07T09:15:00Z'
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleAddSubmit = async (e) => {
    e.preventDefault();

    if (!addForm.student_id || !addForm.name || !addForm.class_name) {
      toast.error('请填写必填字段');
      return;
    }

    setAdding(true);
    try {
      // Prepare student data
      const studentData = {
        ...addForm,
        role: 'student',
        password: addForm.student_id // Default password is student ID
      };

      // Create student using API service
      await apiService.students.create(studentData);

      // Reset form and close modal
      setShowAddModal(false);
      setAddForm({
        student_id: '',
        name: '',
        class_name: '',
        phone: '',
        email: ''
      });

      // Refresh the list
      fetchStudents();
    } catch (error) {
      console.error('Add student error:', error);
      // Error handling is done in the API service
    } finally {
      setAdding(false);
    }
  };

  // Excel import functionality
  const handleImportExcel = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    setImporting(true);
    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);

        // Process imported data
        processImportedStudents(jsonData);
      } catch (error) {
        console.error('Error importing Excel:', error);
        toast.error('导入失败，请检查文件格式');
        setImporting(false);
      }
    };

    reader.readAsArrayBuffer(file);
    // Reset file input
    event.target.value = '';
  };

  const processImportedStudents = async (data) => {
    const results = {
      success: 0,
      errors: [],
      imported: []
    };

    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      const rowNumber = i + 2; // Account for header row

      try {
        // Validate required fields
        const student_id = row['学号']?.toString().trim();
        const name = row['姓名']?.toString().trim();
        const class_name = row['班级']?.toString().trim();
        const phone = row['手机号']?.toString().trim() || '';
        const email = row['邮箱']?.toString().trim() || '';

        if (!student_id || !name || !class_name) {
          results.errors.push(`第${rowNumber}行：学号、姓名、班级为必填项`);
          continue;
        }

        // Check if student already exists
        const existingStudent = students.find(s => s.student_id === student_id);
        if (existingStudent) {
          results.errors.push(`第${rowNumber}行：学号 ${student_id} 已存在`);
          continue;
        }

        // Create student
        const studentData = {
          student_id,
          name,
          class_name,
          phone,
          email,
          role: 'student',
          password: student_id // Default password
        };

        await apiService.students.create(studentData);
        results.success++;
        results.imported.push({ student_id, name, class_name });

      } catch (error) {
        results.errors.push(`第${rowNumber}行：${error.message}`);
      }
    }

    setImportResults(results);
    setImporting(false);

    if (results.success > 0) {
      toast.success(`成功导入 ${results.success} 个学生`);
      fetchStudents(); // Refresh list
    }

    if (results.errors.length > 0) {
      toast.error(`导入过程中发现 ${results.errors.length} 个错误`);
    }
  };

  // Excel export functionality
  const handleExportExcel = () => {
    try {
      const exportData = students.map(student => ({
        '学号': student.student_id,
        '姓名': student.name,
        '班级': student.class_name,
        '手机号': student.phone || '',
        '邮箱': student.email || '',
        '总分': student.total_score || 0,
        '证书数量': student.certificate_count || 0,
        '状态': student.status === 'active' ? '正常' : '禁用'
      }));

      const ws = XLSX.utils.json_to_sheet(exportData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, '学生信息');

      // Set column widths
      const colWidths = [
        { wch: 12 }, // 学号
        { wch: 10 }, // 姓名
        { wch: 20 }, // 班级
        { wch: 15 }, // 手机号
        { wch: 25 }, // 邮箱
        { wch: 8 },  // 总分
        { wch: 10 }, // 证书数量
        { wch: 8 }   // 状态
      ];
      ws['!cols'] = colWidths;

      XLSX.writeFile(wb, `学生信息_${new Date().toISOString().split('T')[0]}.xlsx`);
      toast.success('导出成功');
    } catch (error) {
      console.error('Export error:', error);
      toast.error('导出失败');
    }
  };

  // Generate Excel template
  const downloadTemplate = () => {
    try {
      const templateData = [
        {
          '学号': '2021001',
          '姓名': '张三',
          '班级': '体育教育21-1班',
          '手机号': '13800138001',
          '邮箱': '<EMAIL>'
        },
        {
          '学号': '2021002',
          '姓名': '李四',
          '班级': '体育教育21-2班',
          '手机号': '13800138002',
          '邮箱': '<EMAIL>'
        }
      ];

      const ws = XLSX.utils.json_to_sheet(templateData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, '学生信息模板');

      // Set column widths
      const colWidths = [
        { wch: 12 }, // 学号
        { wch: 10 }, // 姓名
        { wch: 20 }, // 班级
        { wch: 15 }, // 手机号
        { wch: 25 }  // 邮箱
      ];
      ws['!cols'] = colWidths;

      XLSX.writeFile(wb, '学生信息导入模板.xlsx');
      toast.success('模板下载成功');
    } catch (error) {
      console.error('Template download error:', error);
      toast.error('模板下载失败');
    }
  };

  const handleViewDetail = async (student) => {
    try {
      const response = await fetch(`/api/teacher/students/${student.id}/detail`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSelectedStudent(data);
      } else {
        // Mock detailed data
        setSelectedStudent({
          ...student,
          certificates: [
            {
              id: 1,
              certificate_name: '全国大学生数学建模竞赛',
              category_name: '学科竞赛',
              level: '省级一等奖',
              score: 8,
              status: 'approved',
              award_date: '2024-10-15'
            },
            {
              id: 2,
              certificate_name: '羽毛球比赛',
              category_name: '体育竞赛',
              level: '校级第一名',
              score: 5,
              status: 'approved',
              award_date: '2024-11-01'
            }
          ],
          score_breakdown: {
            professional: 25.0,
            sports: 18.5,
            moral: 22.0,
            base: 25.0
          }
        });
      }
      setShowDetailModal(true);
    } catch (error) {
      console.error('Failed to fetch student detail:', error);
      toast.error('获取学生详情失败');
    }
  };

  const filteredStudents = students.filter(student =>
    student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.student_id.includes(searchTerm) ||
    student.class_name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return <LoadingSpinner text="加载学生列表中..." />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">学生管理</h1>
        <div className="flex space-x-3">
          <button
            onClick={downloadTemplate}
            className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
          >
            📄 下载模板
          </button>
          <button
            onClick={() => setShowImportModal(true)}
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
          >
            📥 批量导入
          </button>
          <button
            onClick={handleExportExcel}
            className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors"
          >
            📤 导出Excel
          </button>
          <button
            onClick={() => setShowAddModal(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            + 添加学生
          </button>
        </div>
      </div>

      {/* Search */}
      <div className="bg-white shadow rounded-lg p-4">
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="搜索学生姓名、学号或班级..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <button
            onClick={fetchStudents}
            className="bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200"
          >
            🔄 刷新
          </button>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-4">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">👥</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">学生总数</dt>
                  <dd className="text-lg font-medium text-gray-900">{students.length}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">📊</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">平均分</dt>
                  <dd className="text-lg font-medium text-blue-600">
                    {students.length > 0 ? (students.reduce((sum, s) => sum + s.total_score, 0) / students.length).toFixed(1) : 0}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">🏆</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">最高分</dt>
                  <dd className="text-lg font-medium text-green-600">
                    {students.length > 0 ? Math.max(...students.map(s => s.total_score)) : 0}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">📜</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">证书总数</dt>
                  <dd className="text-lg font-medium text-purple-600">
                    {students.reduce((sum, s) => sum + s.certificate_count, 0)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Students Table */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  学生信息
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  班级
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  总分
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  证书数
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  最后登录
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredStudents.length === 0 ? (
                <tr>
                  <td colSpan="6" className="px-6 py-8 text-center text-gray-500">
                    <span className="text-4xl mb-4 block">👥</span>
                    <p>暂无学生记录</p>
                  </td>
                </tr>
              ) : (
                filteredStudents.map((student) => (
                  <tr key={student.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                            <span className="text-sm font-medium text-blue-600">
                              {student.name.charAt(0)}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {student.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {student.student_id}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {student.class_name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm font-medium text-blue-600">
                        {student.total_score}分
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {student.certificate_count}个
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {student.last_login ? new Date(student.last_login).toLocaleDateString() : '从未登录'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => handleViewDetail(student)}
                        className="text-blue-600 hover:text-blue-900 mr-3"
                      >
                        查看详情
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add Student Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">添加学生</h3>
              <form onSubmit={handleAddSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">学号 *</label>
                  <input
                    type="text"
                    value={addForm.student_id}
                    onChange={(e) => setAddForm({...addForm, student_id: e.target.value})}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">姓名 *</label>
                  <input
                    type="text"
                    value={addForm.name}
                    onChange={(e) => setAddForm({...addForm, name: e.target.value})}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">班级 *</label>
                  <input
                    type="text"
                    value={addForm.class_name}
                    onChange={(e) => setAddForm({...addForm, class_name: e.target.value})}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">手机号</label>
                  <input
                    type="tel"
                    value={addForm.phone}
                    onChange={(e) => setAddForm({...addForm, phone: e.target.value})}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">邮箱</label>
                  <input
                    type="email"
                    value={addForm.email}
                    onChange={(e) => setAddForm({...addForm, email: e.target.value})}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowAddModal(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                    disabled={adding}
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={adding}
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"
                  >
                    {adding ? '添加中...' : '添加'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Student Detail Modal */}
      {showDetailModal && selectedStudent && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-10 mx-auto p-5 border w-4/5 max-w-4xl shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">学生详情</h3>
                <button
                  onClick={() => setShowDetailModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>
              
              {/* Student Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-3">基本信息</h4>
                  <div className="space-y-2 text-sm">
                    <p><span className="font-medium">姓名:</span> {selectedStudent.name}</p>
                    <p><span className="font-medium">学号:</span> {selectedStudent.student_id}</p>
                    <p><span className="font-medium">班级:</span> {selectedStudent.class_name}</p>
                    <p><span className="font-medium">手机:</span> {selectedStudent.phone || '未填写'}</p>
                    <p><span className="font-medium">邮箱:</span> {selectedStudent.email || '未填写'}</p>
                  </div>
                </div>

                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-3">成绩统计</h4>
                  <div className="space-y-2 text-sm">
                    <p><span className="font-medium">总分:</span> <span className="text-blue-600 font-semibold">{selectedStudent.total_score}分</span></p>
                    <p><span className="font-medium">证书数量:</span> {selectedStudent.certificate_count}个</p>
                    {selectedStudent.score_breakdown && (
                      <>
                        <p><span className="font-medium">专业类:</span> {selectedStudent.score_breakdown.professional}分</p>
                        <p><span className="font-medium">体育美育:</span> {selectedStudent.score_breakdown.sports}分</p>
                        <p><span className="font-medium">文明品德:</span> {selectedStudent.score_breakdown.moral}分</p>
                        <p><span className="font-medium">基础分:</span> {selectedStudent.score_breakdown.base}分</p>
                      </>
                    )}
                  </div>
                </div>
              </div>

              {/* Certificates */}
              {selectedStudent.certificates && selectedStudent.certificates.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">证书记录</h4>
                  <div className="space-y-2">
                    {selectedStudent.certificates.map(cert => (
                      <div key={cert.id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                        <div>
                          <p className="font-medium text-gray-900">{cert.certificate_name}</p>
                          <p className="text-sm text-gray-600">{cert.category_name} · {cert.level}</p>
                        </div>
                        <div className="text-right">
                          <span className="text-green-600 font-semibold">+{cert.score}分</span>
                          <p className="text-xs text-gray-500">{new Date(cert.award_date).toLocaleDateString()}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Import Modal */}
      {showImportModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">批量导入学生</h3>

              {!importing && !importResults && (
                <div className="space-y-4">
                  <div className="text-sm text-gray-600">
                    <p className="mb-2">请按照以下格式准备Excel文件：</p>
                    <ul className="list-disc list-inside space-y-1 text-xs">
                      <li>第一行为表头：学号、姓名、班级、手机号、邮箱</li>
                      <li>学号、姓名、班级为必填项</li>
                      <li>手机号和邮箱为可选项</li>
                      <li>默认密码为学号</li>
                    </ul>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      选择Excel文件
                    </label>
                    <input
                      type="file"
                      accept=".xlsx,.xls"
                      onChange={handleImportExcel}
                      className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                    />
                  </div>

                  <div className="flex justify-end space-x-3 pt-4">
                    <button
                      onClick={() => setShowImportModal(false)}
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                    >
                      取消
                    </button>
                  </div>
                </div>
              )}

              {importing && (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-gray-600">正在导入学生信息...</p>
                </div>
              )}

              {importResults && (
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-2xl mb-2">
                      {importResults.success > 0 ? '✅' : '❌'}
                    </div>
                    <h4 className="text-lg font-medium text-gray-900">导入完成</h4>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">成功导入：</span>
                        <span className="font-medium text-green-600">{importResults.success} 个</span>
                      </div>
                      <div>
                        <span className="text-gray-600">导入失败：</span>
                        <span className="font-medium text-red-600">{importResults.errors.length} 个</span>
                      </div>
                    </div>
                  </div>

                  {importResults.errors.length > 0 && (
                    <div>
                      <h5 className="text-sm font-medium text-gray-900 mb-2">错误详情：</h5>
                      <div className="max-h-32 overflow-y-auto bg-red-50 p-3 rounded text-xs">
                        {importResults.errors.map((error, index) => (
                          <div key={index} className="text-red-700 mb-1">{error}</div>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="flex justify-end space-x-3 pt-4">
                    <button
                      onClick={() => {
                        setShowImportModal(false);
                        setImportResults(null);
                      }}
                      className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
                    >
                      完成
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StudentManagement;
