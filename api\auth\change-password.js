import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Helper function to verify user authentication
async function verifyAuth(req) {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('未提供认证令牌');
  }

  const token = authHeader.substring(7);
  
  // Verify token with Supabase
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    throw new Error('令牌无效或已过期');
  }

  // Get user profile from database
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('*')
    .eq('id', user.id)
    .single();

  if (userError || !userData) {
    throw new Error('用户不存在');
  }

  return userData;
}

export default async function handler(req, res) {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,PATCH,DELETE,POST,PUT');
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization'
  );

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Verify authentication
    const user = await verifyAuth(req);

    const { old_password, new_password } = req.body;

    // Input validation
    if (!old_password || !new_password) {
      return res.status(400).json({ message: '请提供当前密码和新密码' });
    }

    // Validate new password strength
    if (new_password.length < 6) {
      return res.status(400).json({ message: '新密码长度至少6位' });
    }

    // Verify old password
    const isOldPasswordValid = await bcrypt.compare(old_password, user.password_hash);
    
    if (!isOldPasswordValid) {
      // Log failed password change attempt
      await supabase
        .from('operation_logs')
        .insert({
          user_id: user.id,
          action: '修改密码失败',
          details: {
            reason: 'wrong_old_password',
            ip: req.headers['x-forwarded-for'] || req.connection.remoteAddress
          },
          target_type: 'auth',
          ip_address: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
          user_agent: req.headers['user-agent']
        });

      return res.status(400).json({ message: '当前密码错误' });
    }

    // Hash new password
    const newPasswordHash = await bcrypt.hash(new_password, 10);

    // Update password in database
    const { error: updateError } = await supabase
      .from('users')
      .update({ 
        password_hash: newPasswordHash,
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id);

    if (updateError) {
      throw updateError;
    }

    // Update password in Supabase Auth
    const { error: authUpdateError } = await supabase.auth.admin.updateUserById(
      user.id,
      { password: new_password }
    );

    if (authUpdateError) {
      console.error('Auth password update error:', authUpdateError);
      // Don't fail the request if auth update fails, as the database is updated
    }

    // Log successful password change
    await supabase
      .from('operation_logs')
      .insert({
        user_id: user.id,
        action: '修改密码',
        details: {
          success: true,
          ip: req.headers['x-forwarded-for'] || req.connection.remoteAddress
        },
        target_type: 'auth',
        ip_address: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
        user_agent: req.headers['user-agent']
      });

    res.status(200).json({
      message: '密码修改成功',
      data: {
        success: true
      }
    });

  } catch (error) {
    console.error('Change password error:', error);
    
    // Log system error
    await supabase
      .from('operation_logs')
      .insert({
        user_id: null,
        action: '修改密码系统错误',
        details: {
          error: error.message,
          ip: req.headers['x-forwarded-for'] || req.connection.remoteAddress
        },
        target_type: 'system',
        ip_address: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
        user_agent: req.headers['user-agent']
      });

    return res.status(500).json({ 
      message: error.message || '密码修改失败' 
    });
  }
}
