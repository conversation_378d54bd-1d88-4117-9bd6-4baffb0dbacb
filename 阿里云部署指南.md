# 🚀 阿里云部署指南 - 详细步骤

## 💰 成本预算
- **学生用户**：9.5元/月（需学生认证）
- **新用户优惠**：首年99元（1核2G配置）
- **轻量应用服务器**：24元/月起
- **域名**：首年1元（可选）

## 📋 准备工作

### 1. 购买服务器
1. 访问 [阿里云官网](https://www.aliyun.com)
2. 选择 **轻量应用服务器** 或 **ECS云服务器**
3. 推荐配置：
   - **CPU**：1核
   - **内存**：2GB
   - **系统盘**：40GB
   - **操作系统**：Ubuntu 20.04 LTS

### 2. 基础配置
```bash
# 连接服务器（使用阿里云控制台或SSH工具）
ssh root@你的服务器IP

# 更新系统
apt update && apt upgrade -y

# 安装必要软件
apt install -y curl wget git unzip
```

## 🛠️ 环境安装

### 1. 安装Node.js
```bash
# 安装Node.js 18 LTS
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
apt-get install -y nodejs

# 验证安装
node --version
npm --version
```

### 2. 安装PM2（进程管理器）
```bash
npm install -g pm2
```

### 3. 配置防火墙
```bash
# Ubuntu防火墙配置
ufw allow ssh
ufw allow 80
ufw allow 443
ufw allow 5000
ufw --force enable
```

## 📦 项目部署

### 1. 上传项目文件
**方法一：使用Git（推荐）**
```bash
# 如果您的项目在GitHub上
git clone https://github.com/你的用户名/项目名.git
cd 项目名
```

**方法二：直接上传**
- 使用WinSCP、FileZilla等工具
- 将整个项目文件夹上传到服务器

### 2. 安装依赖和构建
```bash
# 安装项目依赖
npm install

# 构建前端
cd client
npm run build
cd ..

# 配置生产环境
cd server
cp .env.production .env
```

### 3. 启动服务
```bash
# 使用PM2启动服务
pm2 start sqlite-server.js --name "quality-assessment"

# 设置开机自启
pm2 startup
pm2 save

# 查看服务状态
pm2 status
```

## 🌐 域名配置（可选）

### 1. 购买域名
- 阿里云域名：首年1元起
- 或使用其他域名服务商

### 2. 域名解析
```
类型: A
主机记录: @
记录值: 你的服务器IP
TTL: 600
```

### 3. 配置Nginx反向代理
```bash
# 安装Nginx
apt install -y nginx

# 创建配置文件
nano /etc/nginx/sites-available/quality-assessment
```

Nginx配置内容：
```nginx
server {
    listen 80;
    server_name 你的域名.com;
    
    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

```bash
# 启用配置
ln -s /etc/nginx/sites-available/quality-assessment /etc/nginx/sites-enabled/
nginx -t
systemctl restart nginx
```

## 🔒 SSL证书配置（免费HTTPS）

```bash
# 安装Certbot
apt install -y certbot python3-certbot-nginx

# 获取SSL证书
certbot --nginx -d 你的域名.com

# 设置自动续期
crontab -e
# 添加以下行：
0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 监控和维护

### 1. 查看服务状态
```bash
# PM2状态
pm2 status
pm2 logs quality-assessment

# 系统资源
htop
df -h
```

### 2. 数据备份
```bash
# 创建备份脚本
nano /root/backup.sh
```

备份脚本内容：
```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/root/backups"
PROJECT_DIR="/root/项目目录"

mkdir -p $BACKUP_DIR

# 备份数据文件
cp $PROJECT_DIR/server/data.json $BACKUP_DIR/data_$DATE.json

# 备份上传文件
tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz $PROJECT_DIR/server/uploads/

# 删除7天前的备份
find $BACKUP_DIR -name "*.json" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

```bash
# 设置定时备份
chmod +x /root/backup.sh
crontab -e
# 添加：每天凌晨2点备份
0 2 * * * /root/backup.sh
```

## 🔧 性能优化

### 1. 数据库升级（可选）
如果用户量大，建议升级到MySQL：

```bash
# 安装MySQL
apt install -y mysql-server

# 安全配置
mysql_secure_installation
```

### 2. 启用Gzip压缩
在Nginx配置中添加：
```nginx
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
```

## 📱 访问地址

部署完成后，用户可通过以下地址访问：
- **IP访问**：`http://你的服务器IP:5000`
- **域名访问**：`https://你的域名.com`（配置域名后）

## 💡 优势对比

### 阿里云 vs 内网穿透

| 对比项 | 阿里云部署 | 内网穿透 |
|--------|------------|----------|
| **安全性** | ⭐⭐⭐⭐⭐ 高 | ⭐⭐ 中等 |
| **稳定性** | ⭐⭐⭐⭐⭐ 24/7稳定 | ⭐⭐ 依赖本地网络 |
| **性能** | ⭐⭐⭐⭐ 专业服务器 | ⭐⭐⭐ 受限于本地带宽 |
| **成本** | ⭐⭐⭐ 9.5-24元/月 | ⭐⭐⭐⭐⭐ 完全免费 |
| **维护** | ⭐⭐⭐ 需要基础运维 | ⭐⭐⭐⭐ 几乎无需维护 |

## 🎯 推荐方案

**如果您的预算允许（每月10-25元）**，强烈推荐阿里云部署：
- 更安全可靠
- 24小时稳定运行
- 专业的服务器环境
- 可以配置自己的域名

**如果预算紧张**，可以先用内网穿透测试，后期再迁移到云服务器。
