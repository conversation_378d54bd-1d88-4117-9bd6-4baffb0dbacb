/* 教师端布局样式 */

/* Header 用户下拉菜单样式 */
.teacher-header-user-dropdown {
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px solid transparent;
  transition: all 0.2s ease;
  background-color: #fafafa;
  min-width: 140px;
}

.teacher-header-user-dropdown:hover {
  background-color: #f0f0f0;
  border-color: #d9d9d9;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.teacher-header-user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  line-height: 1.2;
  min-width: 0;
}

.teacher-header-user-name {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 2px;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.teacher-header-user-id {
  font-size: 12px;
  color: #8c8c8c;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Header 整体样式优化 */
.teacher-header {
  background: #fff;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  height: 64px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}

.teacher-header-left {
  display: flex;
  align-items: center;
  flex: 1 1 auto;
  min-width: 0;
  margin-right: 16px;
}

.teacher-header-title {
  font-size: 18px;
  color: #262626;
  font-weight: 600;
  white-space: nowrap;
}

.teacher-header-role-badge {
  margin-left: 12px;
  padding: 4px 8px;
  color: white;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 500;
  white-space: nowrap;
}

.teacher-header-role-badge.super-admin {
  background: #1890ff;
}

.teacher-header-role-badge.admin {
  background: #52c41a;
}

.teacher-header-role-badge.user {
  background: #faad14;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .teacher-header {
    padding: 0 16px;
  }
  
  .teacher-header-title {
    font-size: 16px;
  }
  
  .teacher-header-user-dropdown {
    min-width: 120px;
    padding: 6px 8px;
  }
  
  .teacher-header-user-name,
  .teacher-header-user-id {
    max-width: 80px;
  }
}

@media (max-width: 480px) {
  .teacher-header {
    padding: 0 12px;
  }
  
  .teacher-header-title {
    font-size: 14px;
  }
  
  .teacher-header-role-badge {
    margin-left: 8px;
    padding: 2px 6px;
    font-size: 10px;
  }
  
  .teacher-header-user-dropdown {
    min-width: 100px;
    padding: 4px 6px;
  }
  
  .teacher-header-user-name {
    font-size: 13px;
    max-width: 60px;
  }
  
  .teacher-header-user-id {
    font-size: 11px;
    max-width: 60px;
  }
}
