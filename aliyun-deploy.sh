#!/bin/bash

# 阿里云自动部署脚本
# 使用方法: chmod +x aliyun-deploy.sh && ./aliyun-deploy.sh

set -e

echo "=========================================="
echo "    学生素质测评系统 - 阿里云自动部署"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}请使用root用户运行此脚本${NC}"
    exit 1
fi

echo -e "${YELLOW}🔍 正在检查系统环境...${NC}"

# 更新系统
echo -e "${YELLOW}📦 更新系统包...${NC}"
apt update && apt upgrade -y

# 安装基础软件
echo -e "${YELLOW}🛠️ 安装基础软件...${NC}"
apt install -y curl wget git unzip htop

# 安装Node.js
echo -e "${YELLOW}📦 安装Node.js...${NC}"
if ! command -v node &> /dev/null; then
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    apt-get install -y nodejs
fi

NODE_VERSION=$(node --version)
echo -e "${GREEN}✅ Node.js版本: $NODE_VERSION${NC}"

# 安装PM2
echo -e "${YELLOW}📦 安装PM2进程管理器...${NC}"
npm install -g pm2

# 配置防火墙
echo -e "${YELLOW}🔒 配置防火墙...${NC}"
ufw allow ssh
ufw allow 80
ufw allow 443
ufw allow 5000
ufw --force enable

# 创建项目目录
PROJECT_DIR="/opt/quality-assessment"
echo -e "${YELLOW}📁 创建项目目录: $PROJECT_DIR${NC}"
mkdir -p $PROJECT_DIR

# 提示用户上传项目文件
echo -e "${YELLOW}📤 请将项目文件上传到: $PROJECT_DIR${NC}"
echo -e "${YELLOW}您可以使用以下方法之一:${NC}"
echo "1. 使用SCP: scp -r 本地项目路径/* root@服务器IP:$PROJECT_DIR/"
echo "2. 使用Git: git clone 项目地址 $PROJECT_DIR"
echo "3. 使用FTP工具上传"
echo ""
read -p "项目文件已上传完成？(y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${RED}请先上传项目文件，然后重新运行此脚本${NC}"
    exit 1
fi

# 检查项目文件
if [ ! -f "$PROJECT_DIR/package.json" ]; then
    echo -e "${RED}❌ 未找到package.json文件，请确认项目文件已正确上传${NC}"
    exit 1
fi

cd $PROJECT_DIR

# 安装项目依赖
echo -e "${YELLOW}📦 安装项目依赖...${NC}"
npm install

# 构建前端
echo -e "${YELLOW}🏗️ 构建前端应用...${NC}"
cd client
npm run build
cd ..

# 配置生产环境
echo -e "${YELLOW}⚙️ 配置生产环境...${NC}"
cd server
if [ -f ".env.production" ]; then
    cp .env.production .env
    echo -e "${GREEN}✅ 已配置生产环境${NC}"
else
    echo -e "${YELLOW}⚠️ 未找到.env.production文件，使用默认配置${NC}"
fi

# 启动服务
echo -e "${YELLOW}🚀 启动服务...${NC}"
pm2 start sqlite-server.js --name "quality-assessment"
pm2 startup
pm2 save

# 安装Nginx（可选）
read -p "是否安装Nginx反向代理？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}📦 安装Nginx...${NC}"
    apt install -y nginx
    
    # 创建Nginx配置
    cat > /etc/nginx/sites-available/quality-assessment << EOF
server {
    listen 80;
    server_name _;
    
    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF
    
    # 启用配置
    ln -sf /etc/nginx/sites-available/quality-assessment /etc/nginx/sites-enabled/
    rm -f /etc/nginx/sites-enabled/default
    nginx -t && systemctl restart nginx
    
    echo -e "${GREEN}✅ Nginx配置完成${NC}"
fi

# 创建备份脚本
echo -e "${YELLOW}💾 创建备份脚本...${NC}"
mkdir -p /root/backups

cat > /root/backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/root/backups"
PROJECT_DIR="/opt/quality-assessment"

mkdir -p $BACKUP_DIR

# 备份数据文件
if [ -f "$PROJECT_DIR/server/data.json" ]; then
    cp $PROJECT_DIR/server/data.json $BACKUP_DIR/data_$DATE.json
fi

# 备份上传文件
if [ -d "$PROJECT_DIR/server/uploads" ]; then
    tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz -C $PROJECT_DIR/server uploads/
fi

# 删除7天前的备份
find $BACKUP_DIR -name "*.json" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "$(date): 备份完成" >> $BACKUP_DIR/backup.log
EOF

chmod +x /root/backup.sh

# 设置定时备份
(crontab -l 2>/dev/null; echo "0 2 * * * /root/backup.sh") | crontab -

# 获取服务器IP
SERVER_IP=$(curl -s ifconfig.me)

echo ""
echo "=========================================="
echo -e "${GREEN}🎉 部署完成！${NC}"
echo "=========================================="
echo -e "${GREEN}📍 访问地址:${NC}"
echo "   http://$SERVER_IP:5000"
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "   http://$SERVER_IP (通过Nginx)"
fi
echo ""
echo -e "${GREEN}🔑 默认账户:${NC}"
echo "   管理员: admin / admin123"
echo ""
echo -e "${GREEN}📊 管理命令:${NC}"
echo "   查看服务状态: pm2 status"
echo "   查看日志: pm2 logs quality-assessment"
echo "   重启服务: pm2 restart quality-assessment"
echo "   手动备份: /root/backup.sh"
echo ""
echo -e "${YELLOW}💡 建议:${NC}"
echo "1. 立即修改管理员密码"
echo "2. 配置域名和SSL证书"
echo "3. 定期检查服务状态"
echo "=========================================="
