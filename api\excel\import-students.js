import { createClient } from '@supabase/supabase-js';
import formidable from 'formidable';
import ExcelJS from 'exceljs';
import bcrypt from 'bcryptjs';
import fs from 'fs';

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Disable body parsing for file uploads
export const config = {
  api: {
    bodyParser: false,
  },
};

// Helper function to verify user authentication and permissions
async function verifyAuth(req, requiredRole = null, requiredPermission = null) {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('未提供认证令牌');
  }

  const token = authHeader.substring(7);
  
  // Verify token with Supabase
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    throw new Error('令牌无效或已过期');
  }

  // Get user profile from database
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('*')
    .eq('id', user.id)
    .single();

  if (userError || !userData) {
    throw new Error('用户不存在');
  }

  // Check role requirement
  if (requiredRole && userData.role !== requiredRole) {
    throw new Error('权限不足');
  }

  // Check permission requirement
  if (requiredPermission && !userData.is_super_admin) {
    if (!userData.permissions || !userData.permissions.includes(requiredPermission)) {
      throw new Error('权限不足');
    }
  }

  return userData;
}

export default async function handler(req, res) {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,PATCH,DELETE,POST,PUT');
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization'
  );

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  let tempFilePath = null;

  try {
    // Verify authentication - require teacher role with student management permission
    const currentUser = await verifyAuth(req, 'teacher', 'student_management');

    // Parse form data
    const form = formidable({
      maxFileSize: 10 * 1024 * 1024, // 10MB
      allowEmptyFiles: false,
    });

    const [fields, files] = await form.parse(req);

    if (!files.file || !files.file[0]) {
      return res.status(400).json({ message: '请选择要导入的Excel文件' });
    }

    const file = files.file[0];
    tempFilePath = file.filepath;

    // Validate file type
    const allowedMimeTypes = [
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];

    if (!allowedMimeTypes.includes(file.mimetype)) {
      return res.status(400).json({ message: '请上传Excel文件 (.xls 或 .xlsx)' });
    }

    // Read Excel file
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(tempFilePath);

    const worksheet = workbook.getWorksheet(1);
    if (!worksheet) {
      return res.status(400).json({ message: 'Excel文件格式错误，找不到工作表' });
    }

    const results = {
      success_count: 0,
      error_count: 0,
      errors: [],
      imported_students: []
    };

    // Process each row (skip header row)
    const rows = worksheet.getRows(2, worksheet.rowCount - 1) || [];
    
    for (let i = 0; i < rows.length; i++) {
      const row = rows[i];
      const rowNumber = i + 2; // Account for header row

      try {
        // Extract data from row
        const student_id = row.getCell(1).text?.trim();
        const name = row.getCell(2).text?.trim();
        const class_name = row.getCell(3).text?.trim();
        const password = row.getCell(4).text?.trim() || student_id; // Default password is student_id

        // Validate required fields
        if (!student_id || !name || !class_name) {
          results.error_count++;
          results.errors.push(`第${rowNumber}行：学号、姓名、班级为必填项`);
          continue;
        }

        // Validate student_id format (basic validation)
        if (student_id.length < 3) {
          results.error_count++;
          results.errors.push(`第${rowNumber}行：学号格式不正确`);
          continue;
        }

        // Check if student_id already exists
        const { data: existingUser, error: checkError } = await supabase
          .from('users')
          .select('id')
          .eq('student_id', student_id)
          .single();

        if (existingUser) {
          results.error_count++;
          results.errors.push(`第${rowNumber}行：学号 ${student_id} 已存在`);
          continue;
        }

        // Hash password
        const passwordHash = await bcrypt.hash(password, 10);

        // Create new student
        const { data: newStudent, error: insertError } = await supabase
          .from('users')
          .insert({
            student_id,
            name,
            class_name,
            password_hash: passwordHash,
            role: 'student'
          })
          .select('id, student_id, name, class_name')
          .single();

        if (insertError) {
          results.error_count++;
          results.errors.push(`第${rowNumber}行：创建学生失败 - ${insertError.message}`);
          continue;
        }

        results.success_count++;
        results.imported_students.push(newStudent);
        
        console.log('导入学生:', newStudent.name, newStudent.student_id);

      } catch (rowError) {
        results.error_count++;
        results.errors.push(`第${rowNumber}行：处理失败 - ${rowError.message}`);
        console.error(`Row ${rowNumber} error:`, rowError);
      }
    }

    // Log the import operation
    await supabase
      .from('operation_logs')
      .insert({
        user_id: currentUser.id,
        action: 'Excel导入学生',
        details: {
          total_rows: rows.length,
          success_count: results.success_count,
          error_count: results.error_count,
          file_name: file.originalFilename
        },
        target_type: 'student',
        ip_address: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
        user_agent: req.headers['user-agent']
      });

    res.status(200).json({
      message: `导入完成：成功 ${results.success_count} 个，失败 ${results.error_count} 个`,
      data: results
    });

  } catch (error) {
    console.error('Excel import error:', error);
    
    // Log system error
    await supabase
      .from('operation_logs')
      .insert({
        user_id: null,
        action: 'Excel导入系统错误',
        details: {
          error: error.message,
          ip: req.headers['x-forwarded-for'] || req.connection.remoteAddress
        },
        target_type: 'system',
        ip_address: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
        user_agent: req.headers['user-agent']
      });

    return res.status(500).json({ 
      message: error.message || '导入失败' 
    });
  } finally {
    // Clean up temporary file
    if (tempFilePath && fs.existsSync(tempFilePath)) {
      try {
        fs.unlinkSync(tempFilePath);
      } catch (cleanupError) {
        console.error('Temp file cleanup error:', cleanupError);
      }
    }
  }
}
